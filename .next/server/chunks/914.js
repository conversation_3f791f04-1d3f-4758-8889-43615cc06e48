"use strict";exports.id=914,exports.ids=[914],exports.modules={16343:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0});let{PrismaClientKnownRequestError:n,PrismaClientUnknownRequestError:r,PrismaClientRustPanicError:s,PrismaClientInitializationError:a,PrismaClientValidationError:o,getPrismaClient:l,sqltag:u,empty:d,join:c,raw:f,skip:h,Decimal:p,Debug:m,objectEnumValues:g,makeStrictEnum:y,Extensions:b,warnOnce:w,defineDmmfProperty:v,Public:E,getRuntime:x,createParam:P}=i(84311),R={};t.Prisma=R,t.$Enums={},R.prismaVersion={client:"6.8.2",engine:"2060c79ba17c6bb9f5823312b6f6b7f4a845738e"},R.PrismaClientKnownRequestError=n,R.PrismaClientUnknownRequestError=r,R.PrismaClientRustPanicError=s,R.PrismaClientInitializationError=a,R.PrismaClientValidationError=o,R.Decimal=p,R.sql=u,R.empty=d,R.join=c,R.raw=f,R.validator=E.validator,R.getExtensionContext=b.getExtensionContext,R.defineExtension=b.defineExtension,R.DbNull=g.instances.DbNull,R.JsonNull=g.instances.JsonNull,R.AnyNull=g.instances.AnyNull,R.NullTypes={DbNull:g.classes.DbNull,JsonNull:g.classes.JsonNull,AnyNull:g.classes.AnyNull};let _=i(33873);t.Prisma.QuizScalarFieldEnum={id:"id",quizId:"quizId",title:"title",description:"description",author:"author",creationDate:"creationDate",tags:"tags",passingScore:"passingScore",timeLimit:"timeLimit",markupFormat:"markupFormat",locale:"locale",formatVersion:"formatVersion",isPublished:"isPublished",creatorId:"creatorId",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.QuestionScalarFieldEnum={id:"id",questionId:"questionId",type:"type",text:"text",points:"points",feedbackCorrect:"feedbackCorrect",feedbackIncorrect:"feedbackIncorrect",media:"media",hint:"hint",dependsOn:"dependsOn",options:"options",correctAnswer:"correctAnswer",correctAnswers:"correctAnswers",caseSensitive:"caseSensitive",trimWhitespace:"trimWhitespace",exactMatch:"exactMatch",stems:"stems",correctPairs:"correctPairs",textTemplate:"textTemplate",blanks:"blanks",minWordCount:"minWordCount",maxWordCount:"maxWordCount",guidelines:"guidelines",quizId:"quizId",questionPoolId:"questionPoolId",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.QuestionPoolScalarFieldEnum={id:"id",poolId:"poolId",title:"title",description:"description",quizId:"quizId",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.SelectionRuleScalarFieldEnum={id:"id",poolId:"poolId",selectCount:"selectCount",randomize:"randomize",shuffleOrder:"shuffleOrder",quizId:"quizId",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.UserScalarFieldEnum={id:"id",name:"name",email:"email",emailVerified:"emailVerified",password:"password",image:"image",role:"role",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.AccountScalarFieldEnum={id:"id",userId:"userId",type:"type",provider:"provider",providerAccountId:"providerAccountId",refresh_token:"refresh_token",access_token:"access_token",expires_at:"expires_at",token_type:"token_type",scope:"scope",id_token:"id_token",session_state:"session_state"},t.Prisma.SessionScalarFieldEnum={id:"id",sessionToken:"sessionToken",userId:"userId",expires:"expires"},t.Prisma.UserResponseScalarFieldEnum={id:"id",userId:"userId",quizId:"quizId",answers:"answers",score:"score",startedAt:"startedAt",completedAt:"completedAt",timeSpent:"timeSpent",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.SortOrder={asc:"asc",desc:"desc"},t.Prisma.QueryMode={default:"default",insensitive:"insensitive"},t.Prisma.ModelName={Quiz:"Quiz",Question:"Question",QuestionPool:"QuestionPool",SelectionRule:"SelectionRule",User:"User",Account:"Account",Session:"Session",UserResponse:"UserResponse"};let S={generator:{name:"client",provider:{fromEnvVar:null,value:"prisma-client-js"},output:{value:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma",fromEnvVar:null},config:{engineType:"library"},binaryTargets:[{fromEnvVar:null,value:"darwin-arm64",native:!0}],previewFeatures:[],sourceFilePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/prisma/schema.prisma",isCustomOutput:!0},relativeEnvPaths:{rootEnvPath:null,schemaEnvPath:"../../../.env"},relativePath:"../../../prisma",clientVersion:"6.8.2",engineVersion:"2060c79ba17c6bb9f5823312b6f6b7f4a845738e",datasourceNames:["db"],activeProvider:"mongodb",inlineDatasources:{db:{url:{fromEnvVar:"DATABASE_URL",value:null}}},inlineSchema:'// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\n// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?\n// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init\n\ngenerator client {\n  provider = "prisma-client-js"\n  output   = "../src/generated/prisma"\n}\n\ndatasource db {\n  provider = "mongodb"\n  url      = env("DATABASE_URL")\n}\n\n// Quiz model\nmodel Quiz {\n  id            String   @id @default(auto()) @map("_id") @db.ObjectId\n  quizId        String   @unique // The quiz_id from the QFJSON format\n  title         String\n  description   String?\n  author        String?\n  creationDate  DateTime @default(now())\n  tags          String[]\n  passingScore  Float?\n  timeLimit     Int? // in minutes\n  markupFormat  String   @default("markdown")\n  locale        String   @default("en-US")\n  formatVersion String   @default("1.1")\n  isPublished   Boolean  @default(false)\n\n  // Relations\n  questions      Question[]\n  questionPools  QuestionPool[]\n  selectionRules SelectionRule[]\n  responses      UserResponse[]\n  creator        User?           @relation("CreatedBy", fields: [creatorId], references: [id])\n  creatorId      String?         @db.ObjectId\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n}\n\n// Question model\nmodel Question {\n  id                String  @id @default(auto()) @map("_id") @db.ObjectId\n  questionId        String // The question_id from the QFJSON format\n  type              String // multiple_choice, true_false, short_answer, etc.\n  text              Json // Can be a string or an object for multilingual support\n  points            Float\n  feedbackCorrect   String?\n  feedbackIncorrect String?\n  media             Json? // Array of media objects\n  hint              Json? // Array of hint objects\n  dependsOn         Json? // Dependency object\n\n  // Type-specific fields stored as JSON\n  options        Json? // For multiple_choice\n  correctAnswer  Json? // For true_false, short_answer\n  correctAnswers Json? // For short_answer\n  caseSensitive  Boolean? // For short_answer\n  trimWhitespace Boolean? // For short_answer\n  exactMatch     Boolean? // For short_answer\n  stems          Json? // For matching\n  correctPairs   Json? // For matching\n  textTemplate   Json? // For fill_in_the_blank\n  blanks         Json? // For fill_in_the_blank\n  minWordCount   Int? // For essay\n  maxWordCount   Int? // For essay\n  guidelines     String? // For essay\n\n  // Relations\n  quiz           Quiz          @relation(fields: [quizId], references: [id], onDelete: Cascade)\n  quizId         String        @db.ObjectId\n  questionPool   QuestionPool? @relation(fields: [questionPoolId], references: [id])\n  questionPoolId String?       @db.ObjectId\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n}\n\n// QuestionPool model\nmodel QuestionPool {\n  id          String     @id @default(auto()) @map("_id") @db.ObjectId\n  poolId      String // The pool_id from the QFJSON format\n  title       String?\n  description String?\n  questions   Question[]\n\n  // Relations\n  quiz   Quiz   @relation(fields: [quizId], references: [id], onDelete: Cascade)\n  quizId String @db.ObjectId\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n}\n\n// SelectionRule model\nmodel SelectionRule {\n  id           String  @id @default(auto()) @map("_id") @db.ObjectId\n  poolId       String // References the pool_id in QuestionPool\n  selectCount  Int\n  randomize    Boolean @default(false)\n  shuffleOrder Boolean @default(false)\n\n  // Relations\n  quiz   Quiz   @relation(fields: [quizId], references: [id], onDelete: Cascade)\n  quizId String @db.ObjectId\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n}\n\n// User model for authentication\nmodel User {\n  id             String         @id @default(auto()) @map("_id") @db.ObjectId\n  name           String?\n  email          String         @unique\n  emailVerified  DateTime?\n  password       String?\n  image          String?\n  role           String         @default("user") // "user", "admin", "instructor"\n  createdQuizzes Quiz[]         @relation("CreatedBy")\n  responses      UserResponse[]\n  accounts       Account[]\n  sessions       Session[]\n  createdAt      DateTime       @default(now())\n  updatedAt      DateTime       @updatedAt\n}\n\n// Account model for OAuth providers\nmodel Account {\n  id                String  @id @default(auto()) @map("_id") @db.ObjectId\n  userId            String  @db.ObjectId\n  type              String\n  provider          String\n  providerAccountId String\n  refresh_token     String? @db.String\n  access_token      String? @db.String\n  expires_at        Int?\n  token_type        String?\n  scope             String?\n  id_token          String? @db.String\n  session_state     String?\n\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([provider, providerAccountId])\n}\n\n// Session model for managing user sessions\nmodel Session {\n  id           String   @id @default(auto()) @map("_id") @db.ObjectId\n  sessionToken String   @unique\n  userId       String   @db.ObjectId\n  expires      DateTime\n  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n}\n\n// UserResponse model to track quiz attempts\nmodel UserResponse {\n  id          String    @id @default(auto()) @map("_id") @db.ObjectId\n  user        User?     @relation(fields: [userId], references: [id])\n  userId      String?   @db.ObjectId\n  quiz        Quiz      @relation(fields: [quizId], references: [id])\n  quizId      String    @db.ObjectId\n  answers     Json // JSON object with question IDs as keys and user answers as values\n  score       Float\n  startedAt   DateTime  @default(now())\n  completedAt DateTime?\n  timeSpent   Int? // in seconds\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n}\n',inlineSchemaHash:"9bb0cfc3669e5ec14a184676f0085184822e6534bfaf8a9f8c3c46e888329edd",copyEngine:!0},A=i(29021);if(S.dirname=__dirname,!A.existsSync(_.join(__dirname,"schema.prisma"))){let e=["src/generated/prisma","generated/prisma"],t=e.find(e=>A.existsSync(_.join(process.cwd(),e,"schema.prisma")))??e[0];S.dirname=_.join(process.cwd(),t),S.isBundled=!0}S.runtimeDataModel=JSON.parse('{"models":{"Quiz":{"dbName":null,"schema":null,"fields":[{"name":"id","dbName":"_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":["ObjectId",[]],"default":{"name":"auto","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"quizId","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"title","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"description","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"author","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"creationDate","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"tags","kind":"scalar","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"passingScore","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Float","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"timeLimit","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"markupFormat","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":"markdown","isGenerated":false,"isUpdatedAt":false},{"name":"locale","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":"en-US","isGenerated":false,"isUpdatedAt":false},{"name":"formatVersion","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":"1.1","isGenerated":false,"isUpdatedAt":false},{"name":"isPublished","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Boolean","nativeType":null,"default":false,"isGenerated":false,"isUpdatedAt":false},{"name":"questions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Question","nativeType":null,"relationName":"QuestionToQuiz","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"questionPools","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"QuestionPool","nativeType":null,"relationName":"QuestionPoolToQuiz","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"selectionRules","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"SelectionRule","nativeType":null,"relationName":"QuizToSelectionRule","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"responses","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"UserResponse","nativeType":null,"relationName":"QuizToUserResponse","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"creator","kind":"object","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"CreatedBy","relationFromFields":["creatorId"],"relationToFields":["id"],"isGenerated":false,"isUpdatedAt":false},{"name":"creatorId","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":["ObjectId",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Question":{"dbName":null,"schema":null,"fields":[{"name":"id","dbName":"_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":["ObjectId",[]],"default":{"name":"auto","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"questionId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"type","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"text","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"points","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Float","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"feedbackCorrect","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"feedbackIncorrect","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"media","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"hint","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"dependsOn","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"options","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"correctAnswer","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"correctAnswers","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"caseSensitive","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Boolean","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"trimWhitespace","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Boolean","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"exactMatch","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Boolean","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"stems","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"correctPairs","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"textTemplate","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"blanks","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"minWordCount","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"maxWordCount","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"guidelines","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"quiz","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Quiz","nativeType":null,"relationName":"QuestionToQuiz","relationFromFields":["quizId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false},{"name":"quizId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":["ObjectId",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"questionPool","kind":"object","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"QuestionPool","nativeType":null,"relationName":"QuestionToQuestionPool","relationFromFields":["questionPoolId"],"relationToFields":["id"],"isGenerated":false,"isUpdatedAt":false},{"name":"questionPoolId","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":["ObjectId",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"QuestionPool":{"dbName":null,"schema":null,"fields":[{"name":"id","dbName":"_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":["ObjectId",[]],"default":{"name":"auto","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"poolId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"title","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"description","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"questions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Question","nativeType":null,"relationName":"QuestionToQuestionPool","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"quiz","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Quiz","nativeType":null,"relationName":"QuestionPoolToQuiz","relationFromFields":["quizId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false},{"name":"quizId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":["ObjectId",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"SelectionRule":{"dbName":null,"schema":null,"fields":[{"name":"id","dbName":"_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":["ObjectId",[]],"default":{"name":"auto","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"poolId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"selectCount","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"randomize","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Boolean","nativeType":null,"default":false,"isGenerated":false,"isUpdatedAt":false},{"name":"shuffleOrder","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Boolean","nativeType":null,"default":false,"isGenerated":false,"isUpdatedAt":false},{"name":"quiz","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Quiz","nativeType":null,"relationName":"QuizToSelectionRule","relationFromFields":["quizId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false},{"name":"quizId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":["ObjectId",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"User":{"dbName":null,"schema":null,"fields":[{"name":"id","dbName":"_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":["ObjectId",[]],"default":{"name":"auto","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"name","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"email","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"emailVerified","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"password","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"image","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"role","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":"user","isGenerated":false,"isUpdatedAt":false},{"name":"createdQuizzes","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Quiz","nativeType":null,"relationName":"CreatedBy","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"responses","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"UserResponse","nativeType":null,"relationName":"UserToUserResponse","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"accounts","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Account","nativeType":null,"relationName":"AccountToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"sessions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Session","nativeType":null,"relationName":"SessionToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Account":{"dbName":null,"schema":null,"fields":[{"name":"id","dbName":"_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":["ObjectId",[]],"default":{"name":"auto","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":["ObjectId",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"type","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"provider","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"providerAccountId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"refresh_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":["String",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"access_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":["String",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"expires_at","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"token_type","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"scope","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"id_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":["String",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"session_state","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"AccountToUser","relationFromFields":["userId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[["provider","providerAccountId"]],"uniqueIndexes":[{"name":null,"fields":["provider","providerAccountId"]}],"isGenerated":false},"Session":{"dbName":null,"schema":null,"fields":[{"name":"id","dbName":"_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":["ObjectId",[]],"default":{"name":"auto","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"sessionToken","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":["ObjectId",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"expires","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"SessionToUser","relationFromFields":["userId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"UserResponse":{"dbName":null,"schema":null,"fields":[{"name":"id","dbName":"_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":["ObjectId",[]],"default":{"name":"auto","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"UserToUserResponse","relationFromFields":["userId"],"relationToFields":["id"],"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":["ObjectId",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"quiz","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Quiz","nativeType":null,"relationName":"QuizToUserResponse","relationFromFields":["quizId"],"relationToFields":["id"],"isGenerated":false,"isUpdatedAt":false},{"name":"quizId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":["ObjectId",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"answers","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"score","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Float","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"startedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"completedAt","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"timeSpent","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false}},"enums":{},"types":{}}'),v(t.Prisma,S.runtimeDataModel),S.engineWasm=void 0,S.compilerWasm=void 0;let{warnEnvConflicts:T}=i(84311);T({rootEnvPath:S.relativeEnvPaths.rootEnvPath&&_.resolve(S.dirname,S.relativeEnvPaths.rootEnvPath),schemaEnvPath:S.relativeEnvPaths.schemaEnvPath&&_.resolve(S.dirname,S.relativeEnvPaths.schemaEnvPath)});let O=l(S);t.PrismaClient=O,Object.assign(t,R),_.join(__dirname,"libquery_engine-darwin-arm64.dylib.node"),_.join(process.cwd(),"src/generated/prisma/libquery_engine-darwin-arm64.dylib.node"),_.join(__dirname,"schema.prisma"),_.join(process.cwd(),"src/generated/prisma/schema.prisma")},84311:(e,t,i)=>{var n=Object.create,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,u=(e,t)=>()=>(e&&(t=e(e=0)),t),d=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),c=(e,t)=>{for(var i in t)r(e,i,{get:t[i],enumerable:!0})},f=(e,t,i,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of a(t))l.call(e,o)||o===i||r(e,o,{get:()=>t[o],enumerable:!(n=s(t,o))||n.enumerable});return e},h=(e,t,i)=>(i=null!=e?n(o(e)):{},f(!t&&e&&e.__esModule?i:r(i,"default",{value:e,enumerable:!0}),e)),p=d((e,t)=>{t.exports=(e,t=process.argv)=>{let i=e.startsWith("-")?"":1===e.length?"-":"--",n=t.indexOf(i+e),r=t.indexOf("--");return -1!==n&&(-1===r||n<r)}}),m=d((e,t)=>{var n,r=i(48161),s=i(7066),a=p(),{env:o}=process;function l(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function u(e,t){if(0===n)return 0;if(a("color=16m")||a("color=full")||a("color=truecolor"))return 3;if(a("color=256"))return 2;if(e&&!t&&void 0===n)return 0;let i=n||0;if("dumb"===o.TERM)return i;if("win32"===process.platform){let e=r.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in o)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in o)||"codeship"===o.CI_NAME?1:i;if("TEAMCITY_VERSION"in o)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(o.TEAMCITY_VERSION)?1:0;if("truecolor"===o.COLORTERM)return 3;if("TERM_PROGRAM"in o){let e=parseInt((o.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(o.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(o.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(o.TERM)||"COLORTERM"in o?1:i}a("no-color")||a("no-colors")||a("color=false")||a("color=never")?n=0:(a("color")||a("colors")||a("color=true")||a("color=always"))&&(n=1),"FORCE_COLOR"in o&&(n="true"===o.FORCE_COLOR?1:"false"===o.FORCE_COLOR?0:0===o.FORCE_COLOR.length?1:Math.min(parseInt(o.FORCE_COLOR,10),3)),t.exports={supportsColor:function(e){return l(u(e,e&&e.isTTY))},stdout:l(u(!0,s.isatty(1))),stderr:l(u(!0,s.isatty(2)))}}),g=d((e,t)=>{var i=m(),n=p();function r(e){if(/^\d{3,4}$/.test(e)){let t=/(\d{1,2})(\d{2})/.exec(e)||[];return{major:0,minor:parseInt(t[1],10),patch:parseInt(t[2],10)}}let t=(e||"").split(".").map(e=>parseInt(e,10));return{major:t[0],minor:t[1],patch:t[2]}}function s(e){let{CI:t,FORCE_HYPERLINK:s,NETLIFY:a,TEAMCITY_VERSION:o,TERM_PROGRAM:l,TERM_PROGRAM_VERSION:u,VTE_VERSION:d,TERM:c}=process.env;if(s)return!(s.length>0&&0===parseInt(s,10));if(n("no-hyperlink")||n("no-hyperlinks")||n("hyperlink=false")||n("hyperlink=never"))return!1;if(n("hyperlink=true")||n("hyperlink=always")||a)return!0;if(!i.supportsColor(e)||e&&!e.isTTY)return!1;if("WT_SESSION"in process.env)return!0;if("win32"===process.platform||t||o)return!1;if(l){let e=r(u||"");switch(l){case"iTerm.app":return 3===e.major?e.minor>=1:e.major>3;case"WezTerm":return e.major>=0x1343cac;case"vscode":return e.major>1||1===e.major&&e.minor>=72;case"ghostty":return!0}}if(d){if("0.50.0"===d)return!1;let e=r(d);return e.major>0||e.minor>=50}return"alacritty"===c}t.exports={supportsHyperlink:s,stdout:s(process.stdout),stderr:s(process.stderr)}}),y=d((e,t)=>{t.exports={name:"@prisma/internals",version:"6.8.2",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.1","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e","@prisma/schema-engine-wasm":"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}}),b=d((e,t)=>{t.exports={name:"@prisma/engines-version",version:"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"2060c79ba17c6bb9f5823312b6f6b7f4a845738e"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}}),w=d(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.enginesVersion=void 0,e.enginesVersion=b().prisma.enginesVersion}),v=d((e,t)=>{t.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((e,t)=>Math.min(e,t.length),1/0):0}}),E=d((e,t)=>{t.exports=(e,t=1,i)=>{if(i={indent:" ",includeEmptyLines:!1,...i},"string"!=typeof e)throw TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if("number"!=typeof t)throw TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if("string"!=typeof i.indent)throw TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof i.indent}\``);if(0===t)return e;let n=i.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,i.indent.repeat(t))}}),x=d((e,t)=>{t.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")}),P=d((e,t)=>{var i=x();t.exports=e=>"string"==typeof e?e.replace(i(),""):e}),R=d((e,t)=>{t.exports={name:"dotenv",version:"16.5.0",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},homepage:"https://github.com/motdotla/dotenv#readme",funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}}),_=d((e,t)=>{var n=i(73024),r=i(76760),s=i(48161),a=i(77598),o=R().version,l=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function u(e){console.log(`[dotenv@${o}][DEBUG] ${e}`)}function d(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function c(e){let t=null;if(e&&e.path&&e.path.length>0){if(Array.isArray(e.path))for(let i of e.path)n.existsSync(i)&&(t=i.endsWith(".vault")?i:`${i}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`}else t=r.resolve(process.cwd(),".env.vault");return n.existsSync(t)?t:null}function f(e){return"~"===e[0]?r.join(s.homedir(),e.slice(1)):e}var h={configDotenv:function(e){let t=r.resolve(process.cwd(),".env"),i="utf8",s=!!(e&&e.debug);e&&e.encoding?i=e.encoding:s&&u("No encoding is specified. UTF-8 is used by default");let a=[t];if(e&&e.path){if(Array.isArray(e.path))for(let t of(a=[],e.path))a.push(f(t));else a=[f(e.path)]}let o,l={};for(let t of a)try{let r=h.parse(n.readFileSync(t,{encoding:i}));h.populate(l,r,e)}catch(e){s&&u(`Failed to load ${t} ${e.message}`),o=e}let d=process.env;return e&&null!=e.processEnv&&(d=e.processEnv),h.populate(d,l,e),o?{parsed:l,error:o}:{parsed:l}},_configVault:function(e){e&&e.debug&&u("Loading env from encrypted .env.vault");let t=h._parseVault(e),i=process.env;return e&&null!=e.processEnv&&(i=e.processEnv),h.populate(i,t,e),{parsed:t}},_parseVault:function(e){let t=c(e),i=h.configDotenv({path:t});if(!i.parsed){let e=Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw e.code="MISSING_DATA",e}let n=d(e).split(","),r=n.length,s;for(let e=0;e<r;e++)try{let t=n[e].trim(),r=function(e,t){let i;try{i=new URL(t)}catch(e){if("ERR_INVALID_URL"===e.code){let e=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw e.code="INVALID_DOTENV_KEY",e}throw e}let n=i.password;if(!n){let e=Error("INVALID_DOTENV_KEY: Missing key part");throw e.code="INVALID_DOTENV_KEY",e}let r=i.searchParams.get("environment");if(!r){let e=Error("INVALID_DOTENV_KEY: Missing environment part");throw e.code="INVALID_DOTENV_KEY",e}let s=`DOTENV_VAULT_${r.toUpperCase()}`,a=e.parsed[s];if(!a){let e=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${s} in your .env.vault file.`);throw e.code="NOT_FOUND_DOTENV_ENVIRONMENT",e}return{ciphertext:a,key:n}}(i,t);s=h.decrypt(r.ciphertext,r.key);break}catch(t){if(e+1>=r)throw t}return h.parse(s)},config:function(e){var t;if(0===d(e).length)return h.configDotenv(e);let i=c(e);return i?h._configVault(e):(t=`You set DOTENV_KEY but you are missing a .env.vault file at ${i}. Did you forget to build it?`,console.log(`[dotenv@${o}][WARN] ${t}`),h.configDotenv(e))},decrypt:function(e,t){let i=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),r=n.subarray(0,12),s=n.subarray(-16);n=n.subarray(12,-16);try{let e=a.createDecipheriv("aes-256-gcm",i,r);return e.setAuthTag(s),`${e.update(n)}${e.final()}`}catch(n){let e=n instanceof RangeError,t="Invalid key length"===n.message,i="Unsupported state or unable to authenticate data"===n.message;if(e||t){let e=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw e.code="INVALID_DOTENV_KEY",e}if(i){let e=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw e.code="DECRYPTION_FAILED",e}throw n}},parse:function(e){let t,i={},n=e.toString();for(n=n.replace(/\r\n?/mg,`
`);null!=(t=l.exec(n));){let e=t[1],n=t[2]||"",r=(n=n.trim())[0];n=n.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===r&&(n=(n=n.replace(/\\n/g,`
`)).replace(/\\r/g,"\r")),i[e]=n}return i},populate:function(e,t,i={}){let n=!!(i&&i.debug),r=!!(i&&i.override);if("object"!=typeof t){let e=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw e.code="OBJECT_REQUIRED",e}for(let i of Object.keys(t))Object.prototype.hasOwnProperty.call(e,i)?(!0===r&&(e[i]=t[i]),n&&u(!0===r?`"${i}" is already defined and WAS overwritten`:`"${i}" is already defined and was NOT overwritten`)):e[i]=t[i]}};t.exports.configDotenv=h.configDotenv,t.exports._configVault=h._configVault,t.exports._parseVault=h._parseVault,t.exports.config=h.config,t.exports.decrypt=h.decrypt,t.exports.parse=h.parse,t.exports.populate=h.populate,t.exports=h}),S=d((e,t)=>{t.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let i=new URL(`${t}/issues/new`);for(let t of["body","title","labels","template","milestone","assignee","projects"]){let n=e[t];if(void 0!==n){if("labels"===t||"projects"===t){if(!Array.isArray(n))throw TypeError(`The \`${t}\` option should be an array`);n=n.join(",")}i.searchParams.set(t,n)}}return i.toString()},t.exports.default=t.exports}),A=d((e,t)=>{t.exports=function(){function e(e,t,i,n,r){return e<t||i<t?e>i?i+1:e+1:n===r?t:t+1}return function(t,i){if(t===i)return 0;if(t.length>i.length){var n=t;t=i,i=n}for(var r=t.length,s=i.length;r>0&&t.charCodeAt(r-1)===i.charCodeAt(s-1);)r--,s--;for(var a=0;a<r&&t.charCodeAt(a)===i.charCodeAt(a);)a++;if(r-=a,s-=a,0===r||s<3)return s;var o,l,u,d,c,f,h,p,m,g,y,b,w=0,v=[];for(o=0;o<r;o++)v.push(o+1),v.push(t.charCodeAt(a+o));for(var E=v.length-1;w<s-3;)for(m=i.charCodeAt(a+(l=w)),g=i.charCodeAt(a+(u=w+1)),y=i.charCodeAt(a+(d=w+2)),b=i.charCodeAt(a+(c=w+3)),f=w+=4,o=0;o<E;o+=2)l=e(h=v[o],l,u,m,p=v[o+1]),u=e(l,u,d,g,p),d=e(u,d,c,y,p),f=e(d,c,f,b,p),v[o]=f,c=d,d=u,u=l,l=h;for(;w<s;)for(m=i.charCodeAt(a+(l=w)),f=++w,o=0;o<E;o+=2)h=v[o],v[o]=f=e(h,l,f,m,v[o+1]),l=h;return f}}()}),T=u(()=>{}),O=u(()=>{}),q={};c(q,{DMMF:()=>n0,Debug:()=>ex,Decimal:()=>nB,Extensions:()=>I,MetricsClient:()=>r4,PrismaClientInitializationError:()=>iy,PrismaClientKnownRequestError:()=>ib,PrismaClientRustPanicError:()=>iw,PrismaClientUnknownRequestError:()=>iv,PrismaClientValidationError:()=>iE,Public:()=>N,Sql:()=>sa,createParam:()=>rz,defineDmmfProperty:()=>r3,deserializeJsonResponse:()=>nQ,deserializeRawResult:()=>o$,dmmfToRuntimeDataModel:()=>nH,empty:()=>su,getPrismaClient:()=>oH,getRuntime:()=>s5,join:()=>so,makeStrictEnum:()=>oZ,makeTypedQueryFactory:()=>r8,objectEnumValues:()=>rN,raw:()=>sl,serializeJsonQuery:()=>rX,skip:()=>rH,sqltag:()=>sd,warnEnvConflicts:()=>oX,warnOnce:()=>ig}),e.exports=f(r({},"__esModule",{value:!0}),q);var I={};function k(e){return"function"==typeof e?e:t=>t.$extends(e)}function $(e){return e}c(I,{defineExtension:()=>k,getExtensionContext:()=>$});var N={};function D(...e){return e=>e}c(N,{validator:()=>D});var F={};c(F,{$:()=>M,bgBlack:()=>el,bgBlue:()=>ef,bgCyan:()=>ep,bgGreen:()=>ed,bgMagenta:()=>eh,bgRed:()=>eu,bgWhite:()=>em,bgYellow:()=>ec,black:()=>Z,blue:()=>ei,bold:()=>Q,cyan:()=>er,dim:()=>z,gray:()=>ea,green:()=>ee,grey:()=>eo,hidden:()=>K,inverse:()=>H,italic:()=>J,magenta:()=>en,red:()=>X,reset:()=>B,strikethrough:()=>Y,underline:()=>W,white:()=>es,yellow:()=>et});var U,V,j,C,L=!0;"u">typeof process&&({FORCE_COLOR:U,NODE_DISABLE_COLORS:V,NO_COLOR:j,TERM:C}=process.env||{},L=process.stdout&&process.stdout.isTTY);var M={enabled:!V&&null==j&&"dumb"!==C&&(null!=U&&"0"!==U||L)};function G(e,t){let i=RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1b[${e}m`,r=`\x1b[${t}m`;return function(e){return M.enabled&&null!=e?n+(~(""+e).indexOf(r)?e.replace(i,r+n):e)+r:e}}var B=G(0,0),Q=G(1,22),z=G(2,22),J=G(3,23),W=G(4,24),H=G(7,27),K=G(8,28),Y=G(9,29),Z=G(30,39),X=G(31,39),ee=G(32,39),et=G(33,39),ei=G(34,39),en=G(35,39),er=G(36,39),es=G(37,39),ea=G(90,39),eo=G(90,39),el=G(40,49),eu=G(41,49),ed=G(42,49),ec=G(43,49),ef=G(44,49),eh=G(45,49),ep=G(46,49),em=G(47,49),eg=["green","yellow","blue","magenta","cyan","red"],ey=[],eb=Date.now(),ew=0,ev="u">typeof process?process.env:{};globalThis.DEBUG??=ev.DEBUG??"",globalThis.DEBUG_COLORS??=!ev.DEBUG_COLORS||"true"===ev.DEBUG_COLORS;var eE={enable(e){"string"==typeof e&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(e=>e.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),i=t.some(t=>""!==t&&"-"!==t[0]&&e.match(RegExp(t.split("*").join(".*")+"$"))),n=t.some(t=>""!==t&&"-"===t[0]&&e.match(RegExp(t.slice(1).split("*").join(".*")+"$")));return i&&!n},log:(...e)=>{let[t,i,...n]=e;(console.warn??console.log)(`${t} ${i}`,...n)},formatters:{}},ex=new Proxy(function(e){let t={color:eg[ew++%eg.length],enabled:eE.enabled(e),namespace:e,log:eE.log,extend:()=>{}};return new Proxy((...e)=>{let{enabled:i,namespace:n,color:r,log:s}=t;if(0!==e.length&&ey.push([n,...e]),ey.length>100&&ey.shift(),eE.enabled(n)||i){let t=e.map(e=>"string"==typeof e?e:function(e,t=2){let i=new Set;return JSON.stringify(e,(e,t)=>{if("object"==typeof t&&null!==t){if(i.has(t))return"[Circular *]";i.add(t)}else if("bigint"==typeof t)return t.toString();return t},t)}(e)),i=`+${Date.now()-eb}ms`;eb=Date.now(),globalThis.DEBUG_COLORS?s(F[r](Q(n)),...t,F[r](i)):s(n,...t,i)}},{get:(e,i)=>t[i],set:(e,i,n)=>t[i]=n})},{get:(e,t)=>eE[t],set:(e,t,i)=>eE[t]=i}),eP=h(i(73024)),eR="libquery_engine",e_=h(i(31421)),eS=h(i(51455)),eA=h(i(48161)),eT=Symbol.for("@ts-pattern/matcher"),eO=Symbol.for("@ts-pattern/isVariadic"),eq="@ts-pattern/anonymous-select-key",eI=e=>!!(e&&"object"==typeof e),ek=e=>e&&!!e[eT],e$=(e,t,i)=>{if(ek(e)){let{matched:n,selections:r}=e[eT]().match(t);return n&&r&&Object.keys(r).forEach(e=>i(e,r[e])),n}if(eI(e)){if(!eI(t))return!1;if(Array.isArray(e)){if(!Array.isArray(t))return!1;let n=[],r=[],s=[];for(let t of e.keys()){let i=e[t];ek(i)&&i[eO]?s.push(i):s.length?r.push(i):n.push(i)}if(s.length){if(s.length>1)throw Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(t.length<n.length+r.length)return!1;let e=t.slice(0,n.length),a=0===r.length?[]:t.slice(-r.length),o=t.slice(n.length,0===r.length?1/0:-r.length);return n.every((t,n)=>e$(t,e[n],i))&&r.every((e,t)=>e$(e,a[t],i))&&(0===s.length||e$(s[0],o,i))}return e.length===t.length&&e.every((e,n)=>e$(e,t[n],i))}return Reflect.ownKeys(e).every(n=>{let r=e[n];return(n in t||ek(r)&&"optional"===r[eT]().matcherType)&&e$(r,t[n],i)})}return Object.is(t,e)},eN=e=>{var t,i,n;return eI(e)?ek(e)?null!=(t=null==(i=(n=e[eT]()).getSelectionKeys)?void 0:i.call(n))?t:[]:Array.isArray(e)?eD(e,eN):eD(Object.values(e),eN):[]},eD=(e,t)=>e.reduce((e,i)=>e.concat(t(i)),[]);function eF(e){return Object.assign(e,{optional:()=>eF({[eT]:()=>({match:t=>{let i={},n=(e,t)=>{i[e]=t};return void 0===t?(eN(e).forEach(e=>n(e,void 0)),{matched:!0,selections:i}):{matched:e$(e,t,n),selections:i}},getSelectionKeys:()=>eN(e),matcherType:"optional"})}),and:t=>eU(e,t),or:t=>(function(...e){return eF({[eT]:()=>({match:t=>{let i={},n=(e,t)=>{i[e]=t};return eD(e,eN).forEach(e=>n(e,void 0)),{matched:e.some(e=>e$(e,t,n)),selections:i}},getSelectionKeys:()=>eD(e,eN),matcherType:"or"})})})(e,t),select:t=>void 0===t?ej(e):ej(t,e)})}function eU(...e){return eF({[eT]:()=>({match:t=>{let i={},n=(e,t)=>{i[e]=t};return{matched:e.every(e=>e$(e,t,n)),selections:i}},getSelectionKeys:()=>eD(e,eN),matcherType:"and"})})}function eV(e){return{[eT]:()=>({match:t=>({matched:!!e(t)})})}}function ej(...e){let t="string"==typeof e[0]?e[0]:void 0,i=2===e.length?e[1]:"string"==typeof e[0]?void 0:e[0];return eF({[eT]:()=>({match:e=>{let n={[t??eq]:e};return{matched:void 0===i||e$(i,e,(e,t)=>{n[e]=t}),selections:n}},getSelectionKeys:()=>[t??eq].concat(void 0===i?[]:eN(i))})})}function eC(e){return"number"==typeof e}function eL(e){return"string"==typeof e}function eM(e){return"bigint"==typeof e}eF(eV(function(e){return!0}));var eG=e=>Object.assign(eF(e),{startsWith:t=>eG(eU(e,eV(e=>eL(e)&&e.startsWith(t)))),endsWith:t=>eG(eU(e,eV(e=>eL(e)&&e.endsWith(t)))),minLength:t=>eG(eU(e,eV(e=>eL(e)&&e.length>=t))),length:t=>eG(eU(e,eV(e=>eL(e)&&e.length===t))),maxLength:t=>eG(eU(e,eV(e=>eL(e)&&e.length<=t))),includes:t=>eG(eU(e,eV(e=>eL(e)&&e.includes(t)))),regex:t=>eG(eU(e,eV(e=>eL(e)&&!!e.match(t))))}),eB=(eG(eV(eL)),e=>Object.assign(eF(e),{between:(t,i)=>eB(eU(e,eV(e=>eC(e)&&t<=e&&i>=e))),lt:t=>eB(eU(e,eV(e=>eC(e)&&e<t))),gt:t=>eB(eU(e,eV(e=>eC(e)&&e>t))),lte:t=>eB(eU(e,eV(e=>eC(e)&&e<=t))),gte:t=>eB(eU(e,eV(e=>eC(e)&&e>=t))),int:()=>eB(eU(e,eV(e=>eC(e)&&Number.isInteger(e)))),finite:()=>eB(eU(e,eV(e=>eC(e)&&Number.isFinite(e)))),positive:()=>eB(eU(e,eV(e=>eC(e)&&e>0))),negative:()=>eB(eU(e,eV(e=>eC(e)&&e<0)))})),eQ=(eB(eV(eC)),e=>Object.assign(eF(e),{between:(t,i)=>eQ(eU(e,eV(e=>eM(e)&&t<=e&&i>=e))),lt:t=>eQ(eU(e,eV(e=>eM(e)&&e<t))),gt:t=>eQ(eU(e,eV(e=>eM(e)&&e>t))),lte:t=>eQ(eU(e,eV(e=>eM(e)&&e<=t))),gte:t=>eQ(eU(e,eV(e=>eM(e)&&e>=t))),positive:()=>eQ(eU(e,eV(e=>eM(e)&&e>0))),negative:()=>eQ(eU(e,eV(e=>eM(e)&&e<0)))}));eQ(eV(eM)),eF(eV(function(e){return"boolean"==typeof e})),eF(eV(function(e){return"symbol"==typeof e})),eF(eV(function(e){return null==e})),eF(eV(function(e){return null!=e}));var ez=class extends Error{constructor(e){let t;try{t=JSON.stringify(e)}catch{t=e}super(`Pattern matching error: no pattern matches value ${t}`),this.input=void 0,this.input=e}},eJ={matched:!1,value:void 0};function eW(e){return new eH(e,eJ)}var eH=class e{constructor(e,t){this.input=void 0,this.state=void 0,this.input=e,this.state=t}with(...t){if(this.state.matched)return this;let i=t[t.length-1],n=[t[0]],r;3===t.length&&"function"==typeof t[1]?r=t[1]:t.length>2&&n.push(...t.slice(1,t.length-1));let s=!1,a={},o=(e,t)=>{s=!0,a[e]=t},l=n.some(e=>e$(e,this.input,o))&&(!r||r(this.input))?{matched:!0,value:i(s?eq in a?a[eq]:a:this.input,this.input)}:eJ;return new e(this.input,l)}when(t,i){if(this.state.matched)return this;let n=!!t(this.input);return new e(this.input,n?{matched:!0,value:i(this.input,this.input)}:eJ)}otherwise(e){return this.state.matched?this.state.value:e(this.input)}exhaustive(){if(this.state.matched)return this.state.value;throw new ez(this.input)}run(){return this.exhaustive()}returnType(){return this}},eK=i(57975),eY={warn:et("prisma:warn")},eZ={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function eX(e,...t){eZ.warn()&&console.warn(`${eY.warn} ${e}`,...t)}var e0=(0,eK.promisify)(e_.default.exec),e1=ex("prisma:get-platform"),e2=["1.0.x","1.1.x","3.0.x"];async function e4(){let e=eA.default.platform(),t=process.arch;if("freebsd"===e){let e=await ts("freebsd-version");if(e&&e.trim().length>0){let i=/^(\d+)\.?/.exec(e);if(i)return{platform:"freebsd",targetDistro:`freebsd${i[1]}`,arch:t}}}if("linux"!==e)return{platform:e,arch:t};let i=await e3(),n=await ta(),r=eW({arch:t,archFromUname:n,familyDistro:i.familyDistro}).with({familyDistro:"musl"},()=>(e1('Trying platform-specific paths for "alpine"'),["/lib","/usr/lib"])).with({familyDistro:"debian"},({archFromUname:e})=>(e1('Trying platform-specific paths for "debian" (and "ubuntu")'),[`/usr/lib/${e}-linux-gnu`,`/lib/${e}-linux-gnu`])).with({familyDistro:"rhel"},()=>(e1('Trying platform-specific paths for "rhel"'),["/lib64","/usr/lib64"])).otherwise(({familyDistro:e,arch:t,archFromUname:i})=>(e1(`Don't know any platform-specific paths for "${e}" on ${t} (${i})`),[])),{libssl:s}=await e9(r);return{platform:"linux",libssl:s,arch:t,archFromUname:n,...i}}async function e3(){try{var e;let t,i,n,r;return e=await eS.default.readFile("/etc/os-release",{encoding:"utf-8"}),i=(t=/^ID="?([^"\n]*)"?$/im.exec(e))&&t[1]&&t[1].toLowerCase()||"",n=/^ID_LIKE="?([^"\n]*)"?$/im.exec(e),r=eW({id:i,idLike:n&&n[1]&&n[1].toLowerCase()||""}).with({id:"alpine"},({id:e})=>({targetDistro:"musl",familyDistro:e,originalDistro:e})).with({id:"raspbian"},({id:e})=>({targetDistro:"arm",familyDistro:"debian",originalDistro:e})).with({id:"nixos"},({id:e})=>({targetDistro:"nixos",originalDistro:e,familyDistro:"nixos"})).with({id:"debian"},{id:"ubuntu"},({id:e})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:e})).with({id:"rhel"},{id:"centos"},{id:"fedora"},({id:e})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:e})).when(({idLike:e})=>e.includes("debian")||e.includes("ubuntu"),({id:e})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:e})).when(({idLike:e})=>"arch"===i||e.includes("arch"),({id:e})=>({targetDistro:"debian",familyDistro:"arch",originalDistro:e})).when(({idLike:e})=>e.includes("centos")||e.includes("fedora")||e.includes("rhel")||e.includes("suse"),({id:e})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:e})).otherwise(({id:e})=>({targetDistro:void 0,familyDistro:void 0,originalDistro:e})),e1(`Found distro info:
${JSON.stringify(r,null,2)}`),r}catch{return{targetDistro:void 0,familyDistro:void 0,originalDistro:void 0}}}function e6(e){let t=/libssl\.so\.(\d)(\.\d)?/.exec(e);if(t)return e7(`${t[1]}${t[2]??".0"}.x`)}function e7(e){let t=(()=>{if(to(e))return e;let t=e.split(".");return t[1]="0",t.join(".")})();if(e2.includes(t))return t}async function e9(e){let t=await e5(e);if(t){e1(`Found libssl.so file using platform-specific paths: ${t}`);let e=e6(t);if(e1(`The parsed libssl version is: ${e}`),e)return{libssl:e,strategy:"libssl-specific-path"}}e1('Falling back to "ldconfig" and other generic paths');let i=await ts('ldconfig -p | sed "s/.*=>s*//" | sed "s|.*/||" | grep libssl | sort | grep -v "libssl.so.0"');if(i||(i=await e5(["/lib64","/usr/lib64","/lib","/usr/lib"])),i){e1(`Found libssl.so file using "ldconfig" or other generic paths: ${i}`);let e=e6(i);if(e1(`The parsed libssl version is: ${e}`),e)return{libssl:e,strategy:"ldconfig"}}let n=await ts("openssl version -v");if(n){e1(`Found openssl binary with version: ${n}`);let e=function(e){let t=/^OpenSSL\s(\d+\.\d+)\.\d+/.exec(e);if(t)return e7(`${t[1]}.x`)}(n);if(e1(`The parsed openssl version is: ${e}`),e)return{libssl:e,strategy:"openssl-binary"}}return e1("Couldn't find any version of libssl or OpenSSL in the system"),{}}async function e5(e){for(let t of e){let e=await e8(t);if(e)return e}}async function e8(e){try{return(await eS.default.readdir(e)).find(e=>e.startsWith("libssl.so.")&&!e.startsWith("libssl.so.0"))}catch(e){if("ENOENT"===e.code)return;throw e}}async function te(){let{binaryTarget:e}=await tn();return e}async function tt(){let{memoized:e,...t}=await tn();return t}var ti={};async function tn(){if(void 0!==ti.binaryTarget)return Promise.resolve({...ti,memoized:!0});let e=await e4(),t=function(e){let{platform:t,arch:i,archFromUname:n,libssl:r,targetDistro:s,familyDistro:a,originalDistro:o}=e;"linux"!==t||["x64","arm64"].includes(i)||eX(`Prisma only officially supports Linux on amd64 (x86_64) and arm64 (aarch64) system architectures (detected "${i}" instead). If you are using your own custom Prisma engines, you can ignore this warning, as long as you've compiled the engines for your system architecture "${n}".`);let l="1.1.x";if("linux"===t&&void 0===r){let e=eW({familyDistro:a}).with({familyDistro:"debian"},()=>"Please manually install OpenSSL via `apt-get update -y && apt-get install -y openssl` and try installing Prisma again. If you're running Prisma on Docker, add this command to your Dockerfile, or switch to an image that already has OpenSSL installed.").otherwise(()=>"Please manually install OpenSSL and try installing Prisma again.");eX(`Prisma failed to detect the libssl/openssl version to use, and may not work as expected. Defaulting to "openssl-${l}".
${e}`)}let u="debian";if("linux"===t&&void 0===s&&e1(`Distro is "${o}". Falling back to Prisma engines built for "${u}".`),"darwin"===t&&"arm64"===i)return"darwin-arm64";if("darwin"===t)return"darwin";if("win32"===t)return"windows";if("freebsd"===t)return s;if("openbsd"===t)return"openbsd";if("netbsd"===t)return"netbsd";if("linux"===t&&"nixos"===s)return"linux-nixos";if("linux"===t&&"arm64"===i)return`${"musl"===s?"linux-musl-arm64":"linux-arm64"}-openssl-${r||l}`;if("linux"===t&&"arm"===i)return`linux-arm-openssl-${r||l}`;if("linux"===t&&"musl"===s){let e="linux-musl";return!r||to(r)?e:`${e}-openssl-${r}`}return"linux"===t&&s&&r?`${s}-openssl-${r}`:("linux"!==t&&eX(`Prisma detected unknown OS "${t}" and may not work as expected. Defaulting to "linux".`),r?`${u}-openssl-${r}`:s?`${s}-openssl-${l}`:`${u}-openssl-${l}`)}(e);return{...ti={...e,binaryTarget:t},memoized:!1}}async function tr(e){try{return await e()}catch{return}}function ts(e){return tr(async()=>{let t=await e0(e);return e1(`Command "${e}" successfully returned "${t.stdout}"`),t.stdout})}async function ta(){return"function"==typeof eA.default.machine?eA.default.machine():(await ts("uname -m"))?.trim()}function to(e){return e.startsWith("1.")}var tl={};c(tl,{beep:()=>tM,clearScreen:()=>tV,clearTerminal:()=>tj,cursorBackward:()=>tv,cursorDown:()=>tb,cursorForward:()=>tw,cursorGetPosition:()=>tR,cursorHide:()=>tA,cursorLeft:()=>tE,cursorMove:()=>tg,cursorNextLine:()=>t_,cursorPrevLine:()=>tS,cursorRestorePosition:()=>tP,cursorSavePosition:()=>tx,cursorShow:()=>tT,cursorTo:()=>tm,cursorUp:()=>ty,enterAlternativeScreen:()=>tC,eraseDown:()=>t$,eraseEndLine:()=>tq,eraseLine:()=>tk,eraseLines:()=>tO,eraseScreen:()=>tD,eraseStartLine:()=>tI,eraseUp:()=>tN,exitAlternativeScreen:()=>tL,iTerm:()=>tQ,image:()=>tB,link:()=>tG,scrollDown:()=>tU,scrollUp:()=>tF});var tu=h(i(1708),1),td=globalThis.window?.document!==void 0,tc=(globalThis.process?.versions?.node,globalThis.process?.versions?.bun,globalThis.Deno?.version?.deno,globalThis.process?.versions?.electron,globalThis.navigator?.userAgent?.includes("jsdom"),"u">typeof WorkerGlobalScope&&WorkerGlobalScope,"u">typeof DedicatedWorkerGlobalScope&&DedicatedWorkerGlobalScope,"u">typeof SharedWorkerGlobalScope&&SharedWorkerGlobalScope,"u">typeof ServiceWorkerGlobalScope&&ServiceWorkerGlobalScope,globalThis.navigator?.userAgentData?.platform);"macOS"===tc||globalThis.navigator?.platform==="MacIntel"||globalThis.navigator?.userAgent?.includes(" Mac ")===!0||globalThis.process?.platform,"Windows"===tc||globalThis.navigator?.platform==="Win32"||globalThis.process?.platform,"Linux"===tc||globalThis.navigator?.platform?.startsWith("Linux")===!0||globalThis.navigator?.userAgent?.includes(" Linux ")===!0||globalThis.process?.platform,"iOS"===tc||globalThis.navigator?.platform==="MacIntel"&&globalThis.navigator?.maxTouchPoints>1||/iPad|iPhone|iPod/.test(globalThis.navigator?.platform),"Android"===tc||globalThis.navigator?.platform==="Android"||globalThis.navigator?.userAgent?.includes(" Android ")===!0||globalThis.process?.platform;var tf=!td&&"Apple_Terminal"===tu.default.env.TERM_PROGRAM,th=!td&&"win32"===tu.default.platform,tp=td?()=>{throw Error("`process.cwd()` only works in Node.js, not the browser.")}:tu.default.cwd,tm=(e,t)=>{if("number"!=typeof e)throw TypeError("The `x` argument is required");return"number"!=typeof t?"\x1b["+(e+1)+"G":"\x1b["+(t+1)+";"+(e+1)+"H"},tg=(e,t)=>{if("number"!=typeof e)throw TypeError("The `x` argument is required");let i="";return e<0?i+="\x1b["+-e+"D":e>0&&(i+="\x1b["+e+"C"),t<0?i+="\x1b["+-t+"A":t>0&&(i+="\x1b["+t+"B"),i},ty=(e=1)=>"\x1b["+e+"A",tb=(e=1)=>"\x1b["+e+"B",tw=(e=1)=>"\x1b["+e+"C",tv=(e=1)=>"\x1b["+e+"D",tE="\x1b[G",tx=tf?"\x1b7":"\x1b[s",tP=tf?"\x1b8":"\x1b[u",tR="\x1b[6n",t_="\x1b[E",tS="\x1b[F",tA="\x1b[?25l",tT="\x1b[?25h",tO=e=>{let t="";for(let i=0;i<e;i++)t+=tk+(i<e-1?ty():"");return e&&(t+=tE),t},tq="\x1b[K",tI="\x1b[1K",tk="\x1b[2K",t$="\x1b[J",tN="\x1b[1J",tD="\x1b[2J",tF="\x1b[S",tU="\x1b[T",tV="\x1bc",tj=th?`${tD}\x1b[0f`:`${tD}\x1b[3J\x1b[H`,tC="\x1b[?1049h",tL="\x1b[?1049l",tM="\x07",tG=(e,t)=>["\x1b]","8",";",";",t,"\x07",e,"\x1b]","8",";",";","\x07"].join(""),tB=(e,t={})=>{let i=`\x1b]1337;File=inline=1`;return t.width&&(i+=`;width=${t.width}`),t.height&&(i+=`;height=${t.height}`),!1===t.preserveAspectRatio&&(i+=";preserveAspectRatio=0"),i+":"+Buffer.from(e).toString("base64")+"\x07"},tQ={setCwd:(e=tp())=>`\x1b]50;CurrentDir=${e}\x07`,annotation(e,t={}){let i=`\x1b]1337;`,n=void 0!==t.x,r=void 0!==t.y;if((n||r)&&!(n&&r&&void 0!==t.length))throw Error("`x`, `y` and `length` must be defined when `x` or `y` is defined");return e=e.replaceAll("|",""),i+=t.isHidden?"AddHiddenAnnotation=":"AddAnnotation=",t.length>0?i+=(n?[e,t.length,t.x,t.y]:[t.length,e]).join("|"):i+=e,i+"\x07"}},tz=h(g(),1);function tJ(e,t,{target:i="stdout",...n}={}){return tz.default[i]?tl.link(e,t):!1===n.fallback?e:"function"==typeof n.fallback?n.fallback(e,t):`${e} (\u200B${t}\u200B)`}tJ.isSupported=tz.default.stdout,tJ.stderr=(e,t,i={})=>tJ(e,t,{target:"stderr",...i}),tJ.stderr.isSupported=tz.default.stderr;var tW=y().version;function tH(e){let t;return("library"===(t=process.env.PRISMA_CLIENT_ENGINE_TYPE)?"library":"binary"===t?"binary":"client"===t?"client":void 0)||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":e?.previewFeatures.includes("queryCompiler")?"client":"library")}h(w());var tK=h(i(76760));h(w()),ex("prisma:engines"),tK.default.join(__dirname,"../query-engine-darwin"),tK.default.join(__dirname,"../query-engine-darwin-arm64"),tK.default.join(__dirname,"../query-engine-debian-openssl-1.0.x"),tK.default.join(__dirname,"../query-engine-debian-openssl-1.1.x"),tK.default.join(__dirname,"../query-engine-debian-openssl-3.0.x"),tK.default.join(__dirname,"../query-engine-linux-static-x64"),tK.default.join(__dirname,"../query-engine-linux-static-arm64"),tK.default.join(__dirname,"../query-engine-rhel-openssl-1.0.x"),tK.default.join(__dirname,"../query-engine-rhel-openssl-1.1.x"),tK.default.join(__dirname,"../query-engine-rhel-openssl-3.0.x"),tK.default.join(__dirname,"../libquery_engine-darwin.dylib.node"),tK.default.join(__dirname,"../libquery_engine-darwin-arm64.dylib.node"),tK.default.join(__dirname,"../libquery_engine-debian-openssl-1.0.x.so.node"),tK.default.join(__dirname,"../libquery_engine-debian-openssl-1.1.x.so.node"),tK.default.join(__dirname,"../libquery_engine-debian-openssl-3.0.x.so.node"),tK.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.0.x.so.node"),tK.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.1.x.so.node"),tK.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-3.0.x.so.node"),tK.default.join(__dirname,"../libquery_engine-linux-musl.so.node"),tK.default.join(__dirname,"../libquery_engine-linux-musl-openssl-3.0.x.so.node"),tK.default.join(__dirname,"../libquery_engine-rhel-openssl-1.0.x.so.node"),tK.default.join(__dirname,"../libquery_engine-rhel-openssl-1.1.x.so.node"),tK.default.join(__dirname,"../libquery_engine-rhel-openssl-3.0.x.so.node"),tK.default.join(__dirname,"../query_engine-windows.dll.node");var tY=h(i(73024)),tZ=ex("chmodPlusX"),tX=(h(v(),1),"prisma+postgres:");function t0(e){return e?.toString().startsWith(`${tX}//`)??!1}var t1=h(E()),t2=class{constructor(e){this.config=e}toString(){let e,{config:t}=this,i=JSON.parse(JSON.stringify({provider:t.provider.fromEnvVar?`env("${t.provider.fromEnvVar}")`:t.provider.value,binaryTargets:function(e){let t;if(e.length>0){let i=e.find(e=>null!==e.fromEnvVar);t=i?`env("${i.fromEnvVar}")`:e.map(e=>e.native?"native":e.value)}else t=void 0;return t}(t.binaryTargets)}));return`generator ${t.name} {
${(0,t1.default)((e=Object.keys(i).reduce((e,t)=>Math.max(e,t.length),0),Object.entries(i).map(([t,i])=>`${t.padEnd(e)} = ${JSON.parse(JSON.stringify(i,(e,t)=>Array.isArray(t)?`[${t.map(e=>JSON.stringify(e)).join(", ")}]`:JSON.stringify(t)))}`).join(`
`)),2)}
}`}},t4={};c(t4,{error:()=>t8,info:()=>t5,log:()=>t7,query:()=>ie,should:()=>t6,tags:()=>t3,warn:()=>t9});var t3={error:X("prisma:error"),warn:et("prisma:warn"),info:er("prisma:info"),query:ei("prisma:query")},t6={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function t7(...e){console.log(...e)}function t9(e,...t){t6.warn()&&console.warn(`${t3.warn} ${e}`,...t)}function t5(e,...t){console.info(`${t3.info} ${e}`,...t)}function t8(e,...t){console.error(`${t3.error} ${e}`,...t)}function ie(e,...t){console.log(`${t3.query} ${e}`,...t)}function it(e,t){if(!e)throw Error(`${t}. This should never happen. If you see this error, please, open an issue at https://pris.ly/prisma-prisma-bug-report`)}function ii(e,t){throw Error(t)}var ir=h(i(76760)),is=h(_()),ia=h(i(73024)),io=h(i(76760)),il=ex("prisma:tryLoadEnv");function iu({rootEnvPath:e,schemaEnvPath:t},i={conflictCheck:"none"}){let n=id(e);"none"!==i.conflictCheck&&function(e,t,i){let n=e?.dotenvResult.parsed,r=!ic(e?.path,t);if(n&&t&&r&&ia.default.existsSync(t)){let r=is.default.parse(ia.default.readFileSync(t)),s=[];for(let e in r)n[e]===r[e]&&s.push(e);if(s.length>0){let n=io.default.relative(process.cwd(),e.path),r=io.default.relative(process.cwd(),t);if("error"===i)throw Error(`There is a conflict between env var${s.length>1?"s":""} in ${W(n)} and ${W(r)}
Conflicting env vars:
${s.map(e=>`  ${Q(e)}`).join(`
`)}

We suggest to move the contents of ${W(r)} to ${W(n)} to consolidate your env vars.
`);if("warn"===i){let e=`Conflict for env var${s.length>1?"s":""} ${s.map(e=>Q(e)).join(", ")} in ${W(n)} and ${W(r)}
Env vars from ${W(r)} overwrite the ones from ${W(n)}
      `;console.warn(`${et("warn(prisma)")} ${e}`)}}}}(n,t,i.conflictCheck);let r=null;return ic(n?.path,t)||(r=id(t)),n||r||il("No Environment variables loaded"),r?.dotenvResult.error?console.error(X(Q("Schema Env Error: "))+r.dotenvResult.error):{message:[n?.message,r?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...r?.dotenvResult?.parsed}}}function id(e){return e&&ia.default.existsSync(e)?(il(`Environment variables loaded from ${e}`),{dotenvResult:function(e){let t=e.ignoreProcessEnv?{}:process.env,i=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(n,r){let s=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(r);if(!s)return n;let a=s[1],o,l;if("\\"===a)o=(l=s[0]).replace("\\$","$");else{let n=s[2];l=s[0].substring(a.length),o=i(o=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n]||"")}return n.replace(l,o)},n)??n;for(let n in e.parsed){let r=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=i(r)}for(let i in e.parsed)t[i]=e.parsed[i];return e}(is.default.config({path:e,debug:!!process.env.DOTENV_CONFIG_DEBUG||void 0})),message:z(`Environment variables loaded from ${io.default.relative(process.cwd(),e)}`),path:e}):(il(`Environment variables not found at ${e}`),null)}function ic(e,t){return e&&t&&io.default.resolve(e)===io.default.resolve(t)}function ih(e,t){let i={};for(let n of Object.keys(e))i[n]=t(e[n],n);return i}function ip(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var im=new Set,ig=(e,t,...i)=>{im.has(e)||(im.add(e),t9(t,...i))},iy=class e extends Error{constructor(t,i,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=i,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};ip(iy,"PrismaClientInitializationError");var ib=class extends Error{constructor(e,{code:t,clientVersion:i,meta:n,batchRequestIdx:r}){super(e),this.name="PrismaClientKnownRequestError",this.code=t,this.clientVersion=i,this.meta=n,Object.defineProperty(this,"batchRequestIdx",{value:r,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};ip(ib,"PrismaClientKnownRequestError");var iw=class extends Error{constructor(e,t){super(e),this.name="PrismaClientRustPanicError",this.clientVersion=t}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};ip(iw,"PrismaClientRustPanicError");var iv=class extends Error{constructor(e,{clientVersion:t,batchRequestIdx:i}){super(e),this.name="PrismaClientUnknownRequestError",this.clientVersion=t,Object.defineProperty(this,"batchRequestIdx",{value:i,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};ip(iv,"PrismaClientUnknownRequestError");var iE=class extends Error{constructor(e,{clientVersion:t}){super(e),this.name="PrismaClientValidationError",this.clientVersion=t}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};ip(iE,"PrismaClientValidationError");var ix,iP,iR="0123456789abcdef",i_="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",iS="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",iA={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:9e15,crypto:!1},iT=!0,iO="[DecimalError] ",iq=iO+"Invalid argument: ",iI=iO+"Precision limit exceeded",ik=iO+"crypto unavailable",i$="[object Decimal]",iN=Math.floor,iD=Math.pow,iF=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,iU=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,iV=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,ij=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,iC=i_.length-1,iL=iS.length-1,iM={toStringTag:i$};function iG(e){var t,i,n,r=e.length-1,s="",a=e[0];if(r>0){for(s+=a,t=1;t<r;t++)(i=7-(n=e[t]+"").length)&&(s+=i0(i)),s+=n;(i=7-(n=(a=e[t])+"").length)&&(s+=i0(i))}else if(0===a)return"0";for(;a%10==0;)a/=10;return s+a}function iB(e,t,i){if(e!==~~e||e<t||e>i)throw Error(iq+e)}function iQ(e,t,i,n){var r,s,a,o;for(s=e[0];s>=10;s/=10)--t;return--t<0?(t+=7,r=0):(r=Math.ceil((t+1)/7),t%=7),s=iD(10,7-t),o=e[r]%s|0,null==n?t<3?(0==t?o=o/100|0:1==t&&(o=o/10|0),a=i<4&&99999==o||i>3&&49999==o||5e4==o||0==o):a=(i<4&&o+1==s||i>3&&o+1==s/2)&&(e[r+1]/s/100|0)==iD(10,t-2)-1||(o==s/2||0==o)&&(e[r+1]/s/100|0)==0:t<4?(0==t?o=o/1e3|0:1==t?o=o/100|0:2==t&&(o=o/10|0),a=(n||i<4)&&9999==o||!n&&i>3&&4999==o):a=((n||i<4)&&o+1==s||!n&&i>3&&o+1==s/2)&&(e[r+1]/s/1e3|0)==iD(10,t-3)-1,a}function iz(e,t,i){for(var n,r,s=[0],a=0,o=e.length;a<o;){for(r=s.length;r--;)s[r]*=t;for(s[0]+=iR.indexOf(e.charAt(a++)),n=0;n<s.length;n++)s[n]>i-1&&(void 0===s[n+1]&&(s[n+1]=0),s[n+1]+=s[n]/i|0,s[n]%=i)}return s.reverse()}iM.absoluteValue=iM.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),iW(e)},iM.ceil=function(){return iW(new this.constructor(this),this.e+1,2)},iM.clampedTo=iM.clamp=function(e,t){var i=this.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(iq+t);return 0>this.cmp(e)?e:this.cmp(t)>0?t:new i(this)},iM.comparedTo=iM.cmp=function(e){var t,i,n,r,s=this.d,a=(e=new this.constructor(e)).d,o=this.s,l=e.s;if(!s||!a)return o&&l?o!==l?o:s===a?0:!s^o<0?1:-1:NaN;if(!s[0]||!a[0])return s[0]?o:a[0]?-l:0;if(o!==l)return o;if(this.e!==e.e)return this.e>e.e^o<0?1:-1;for(n=s.length,r=a.length,t=0,i=n<r?n:r;t<i;++t)if(s[t]!==a[t])return s[t]>a[t]^o<0?1:-1;return n===r?0:n>r^o<0?1:-1},iM.cosine=iM.cos=function(){var e,t,i=this,n=i.constructor;return i.d?i.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(i.e,i.sd())+7,n.rounding=1,i=function(e,t){var i,n,r;if(t.isZero())return t;(n=t.d.length)<32?r=(1/i8(4,i=Math.ceil(n/3))).toString():(i=16,r="2.3283064365386962890625e-10"),e.precision+=i,t=i5(e,1,t.times(r),new e(1));for(var s=i;s--;){var a=t.times(t);t=a.times(a).minus(a).times(8).plus(1)}return e.precision-=i,t}(n,ne(n,i)),n.precision=e,n.rounding=t,iW(2==iP||3==iP?i.neg():i,e,t,!0)):new n(1):new n(NaN)},iM.cubeRoot=iM.cbrt=function(){var e,t,i,n,r,s,a,o,l,u,d=this.constructor;if(!this.isFinite()||this.isZero())return new d(this);for(iT=!1,(s=this.s*iD(this.s*this,1/3))&&Math.abs(s)!=1/0?n=new d(s.toString()):(i=iG(this.d),(s=((e=this.e)-i.length+1)%3)&&(i+=1==s||-2==s?"0":"00"),s=iD(i,1/3),e=iN((e+1)/3)-(e%3==(e<0?-1:2)),(n=new d(i=s==1/0?"5e"+e:(i=s.toExponential()).slice(0,i.indexOf("e")+1)+e)).s=this.s),a=(e=d.precision)+3;;)if(n=iJ((u=(l=(o=n).times(o).times(o)).plus(this)).plus(this).times(o),u.plus(l),a+2,1),iG(o.d).slice(0,a)===(i=iG(n.d)).slice(0,a)){if("9999"!=(i=i.slice(a-3,a+1))&&(r||"4999"!=i)){+i&&(+i.slice(1)||"5"!=i.charAt(0))||(iW(n,e+1,1),t=!n.times(n).times(n).eq(this));break}if(!r&&(iW(o,e+1,0),o.times(o).times(o).eq(this))){n=o;break}a+=4,r=1}return iT=!0,iW(n,e,d.rounding,t)},iM.decimalPlaces=iM.dp=function(){var e,t=this.d,i=NaN;if(t){if(i=((e=t.length-1)-iN(this.e/7))*7,e=t[e])for(;e%10==0;e/=10)i--;i<0&&(i=0)}return i},iM.dividedBy=iM.div=function(e){return iJ(this,new this.constructor(e))},iM.dividedToIntegerBy=iM.divToInt=function(e){var t=this.constructor;return iW(iJ(this,new t(e),0,1,1),t.precision,t.rounding)},iM.equals=iM.eq=function(e){return 0===this.cmp(e)},iM.floor=function(){return iW(new this.constructor(this),this.e+1,3)},iM.greaterThan=iM.gt=function(e){return this.cmp(e)>0},iM.greaterThanOrEqualTo=iM.gte=function(e){var t=this.cmp(e);return 1==t||0===t},iM.hyperbolicCosine=iM.cosh=function(){var e,t,i,n,r,s=this,a=s.constructor,o=new a(1);if(!s.isFinite())return new a(s.s?1/0:NaN);if(s.isZero())return o;i=a.precision,n=a.rounding,a.precision=i+Math.max(s.e,s.sd())+4,a.rounding=1,(r=s.d.length)<32?t=(1/i8(4,e=Math.ceil(r/3))).toString():(e=16,t="2.3283064365386962890625e-10"),s=i5(a,1,s.times(t),new a(1),!0);for(var l,u=e,d=new a(8);u--;)l=s.times(s),s=o.minus(l.times(d.minus(l.times(d))));return iW(s,a.precision=i,a.rounding=n,!0)},iM.hyperbolicSine=iM.sinh=function(){var e,t,i,n,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(t=s.precision,i=s.rounding,s.precision=t+Math.max(r.e,r.sd())+4,s.rounding=1,(n=r.d.length)<3)r=i5(s,2,r,r,!0);else{e=(e=1.4*Math.sqrt(n))>16?16:0|e,r=i5(s,2,r=r.times(1/i8(5,e)),r,!0);for(var a,o=new s(5),l=new s(16),u=new s(20);e--;)a=r.times(r),r=r.times(o.plus(a.times(l.times(a).plus(u))))}return s.precision=t,s.rounding=i,iW(r,t,i,!0)},iM.hyperbolicTangent=iM.tanh=function(){var e,t,i=this.constructor;return this.isFinite()?this.isZero()?new i(this):(e=i.precision,t=i.rounding,i.precision=e+7,i.rounding=1,iJ(this.sinh(),this.cosh(),i.precision=e,i.rounding=t)):new i(this.s)},iM.inverseCosine=iM.acos=function(){var e=this,t=e.constructor,i=e.abs().cmp(1),n=t.precision,r=t.rounding;return -1!==i?0===i?e.isNeg()?iZ(t,n,r):new t(0):new t(NaN):e.isZero()?iZ(t,n+4,r).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=r,e.times(2))},iM.inverseHyperbolicCosine=iM.acosh=function(){var e,t,i=this,n=i.constructor;return i.lte(1)?new n(i.eq(1)?0:NaN):i.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(i.e),i.sd())+4,n.rounding=1,iT=!1,i=i.times(i).minus(1).sqrt().plus(i),iT=!0,n.precision=e,n.rounding=t,i.ln()):new n(i)},iM.inverseHyperbolicSine=iM.asinh=function(){var e,t,i=this,n=i.constructor;return!i.isFinite()||i.isZero()?new n(i):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(i.e),i.sd())+6,n.rounding=1,iT=!1,i=i.times(i).plus(1).sqrt().plus(i),iT=!0,n.precision=e,n.rounding=t,i.ln())},iM.inverseHyperbolicTangent=iM.atanh=function(){var e,t,i,n,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=s.precision,t=s.rounding,Math.max(n=r.sd(),e)<-(2*r.e)-1?iW(new s(r),e,t,!0):(s.precision=i=n-r.e,r=iJ(r.plus(1),new s(1).minus(r),i+e,1),s.precision=e+4,s.rounding=1,r=r.ln(),s.precision=e,s.rounding=t,r.times(.5))):new s(NaN)},iM.inverseSine=iM.asin=function(){var e,t,i,n,r=this,s=r.constructor;return r.isZero()?new s(r):(t=r.abs().cmp(1),i=s.precision,n=s.rounding,-1!==t?0===t?((e=iZ(s,i+4,n).times(.5)).s=r.s,e):new s(NaN):(s.precision=i+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=n,r.times(2)))},iM.inverseTangent=iM.atan=function(){var e,t,i,n,r,s,a,o,l,u=this,d=u.constructor,c=d.precision,f=d.rounding;if(u.isFinite()){if(u.isZero())return new d(u);if(u.abs().eq(1)&&c+4<=iL)return(a=iZ(d,c+4,f).times(.25)).s=u.s,a}else{if(!u.s)return new d(NaN);if(c+4<=iL)return(a=iZ(d,c+4,f).times(.5)).s=u.s,a}for(d.precision=o=c+10,d.rounding=1,e=i=Math.min(28,o/7+2|0);e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(iT=!1,t=Math.ceil(o/7),n=1,l=u.times(u),a=new d(u),r=u;-1!==e;)if(r=r.times(l),s=a.minus(r.div(n+=2)),r=r.times(l),void 0!==(a=s.plus(r.div(n+=2))).d[t])for(e=t;a.d[e]===s.d[e]&&e--;);return i&&(a=a.times(2<<i-1)),iT=!0,iW(a,d.precision=c,d.rounding=f,!0)},iM.isFinite=function(){return!!this.d},iM.isInteger=iM.isInt=function(){return!!this.d&&iN(this.e/7)>this.d.length-2},iM.isNaN=function(){return!this.s},iM.isNegative=iM.isNeg=function(){return this.s<0},iM.isPositive=iM.isPos=function(){return this.s>0},iM.isZero=function(){return!!this.d&&0===this.d[0]},iM.lessThan=iM.lt=function(e){return 0>this.cmp(e)},iM.lessThanOrEqualTo=iM.lte=function(e){return 1>this.cmp(e)},iM.logarithm=iM.log=function(e){var t,i,n,r,s,a,o,l=this.constructor,u=l.precision,d=l.rounding;if(null==e)e=new l(10),t=!0;else{if(i=(e=new l(e)).d,e.s<0||!i||!i[0]||e.eq(1))return new l(NaN);t=e.eq(10)}if(i=this.d,this.s<0||!i||!i[0]||this.eq(1))return new l(i&&!i[0]?-1/0:1!=this.s?NaN:i?0:1/0);if(t){if(i.length>1)r=!0;else{for(n=i[0];n%10==0;)n/=10;r=1!==n}}if(iT=!1,iQ((o=iJ(i6(this,a=u+5),t?iY(l,a+10):i6(e,a),a,1)).d,n=u,d))do if(a+=10,o=iJ(i6(this,a),t?iY(l,a+10):i6(e,a),a,1),!r){+iG(o.d).slice(n+1,n+15)+1==1e14&&(o=iW(o,u+1,0));break}while(iQ(o.d,n+=10,d));return iT=!0,iW(o,u,d)},iM.minus=iM.sub=function(e){var t,i,n,r,s,a,o,l,u,d,c,f,h=this.constructor;if(e=new h(e),!this.d||!e.d)return this.s&&e.s?this.d?e.s=-e.s:e=new h(e.d||this.s!==e.s?this:NaN):e=new h(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.plus(e);if(u=this.d,f=e.d,o=h.precision,l=h.rounding,!u[0]||!f[0]){if(f[0])e.s=-e.s;else{if(!u[0])return new h(3===l?-0:0);e=new h(this)}return iT?iW(e,o,l):e}if(i=iN(e.e/7),d=iN(this.e/7),u=u.slice(),s=d-i){for((c=s<0)?(t=u,s=-s,a=f.length):(t=f,i=d,a=u.length),s>(n=Math.max(Math.ceil(o/7),a)+2)&&(s=n,t.length=1),t.reverse(),n=s;n--;)t.push(0);t.reverse()}else{for((c=(n=u.length)<(a=f.length))&&(a=n),n=0;n<a;n++)if(u[n]!=f[n]){c=u[n]<f[n];break}s=0}for(c&&(t=u,u=f,f=t,e.s=-e.s),a=u.length,n=f.length-a;n>0;--n)u[a++]=0;for(n=f.length;n>s;){if(u[--n]<f[n]){for(r=n;r&&0===u[--r];)u[r]=1e7-1;--u[r],u[n]+=1e7}u[n]-=f[n]}for(;0===u[--a];)u.pop();for(;0===u[0];u.shift())--i;return u[0]?(e.d=u,e.e=iK(u,i),iT?iW(e,o,l):e):new h(3===l?-0:0)},iM.modulo=iM.mod=function(e){var t,i=this.constructor;return e=new i(e),this.d&&e.s&&(!e.d||e.d[0])?e.d&&(!this.d||this.d[0])?(iT=!1,9==i.modulo?(t=iJ(this,e.abs(),0,3,1),t.s*=e.s):t=iJ(this,e,0,i.modulo,1),t=t.times(e),iT=!0,this.minus(t)):iW(new i(this),i.precision,i.rounding):new i(NaN)},iM.naturalExponential=iM.exp=function(){return i3(this)},iM.naturalLogarithm=iM.ln=function(){return i6(this)},iM.negated=iM.neg=function(){var e=new this.constructor(this);return e.s=-e.s,iW(e)},iM.plus=iM.add=function(e){var t,i,n,r,s,a,o,l,u,d,c=this.constructor;if(e=new c(e),!this.d||!e.d)return this.s&&e.s?this.d||(e=new c(e.d||this.s===e.s?this:NaN)):e=new c(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.minus(e);if(u=this.d,d=e.d,o=c.precision,l=c.rounding,!u[0]||!d[0])return d[0]||(e=new c(this)),iT?iW(e,o,l):e;if(s=iN(this.e/7),n=iN(e.e/7),u=u.slice(),r=s-n){for(r<0?(i=u,r=-r,a=d.length):(i=d,n=s,a=u.length),r>(a=(s=Math.ceil(o/7))>a?s+1:a+1)&&(r=a,i.length=1),i.reverse();r--;)i.push(0);i.reverse()}for((a=u.length)-(r=d.length)<0&&(r=a,i=d,d=u,u=i),t=0;r;)t=(u[--r]=u[r]+d[r]+t)/1e7|0,u[r]%=1e7;for(t&&(u.unshift(t),++n),a=u.length;0==u[--a];)u.pop();return e.d=u,e.e=iK(u,n),iT?iW(e,o,l):e},iM.precision=iM.sd=function(e){var t;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(iq+e);return this.d?(t=iX(this.d),e&&this.e+1>t&&(t=this.e+1)):t=NaN,t},iM.round=function(){var e=this.constructor;return iW(new e(this),this.e+1,e.rounding)},iM.sine=iM.sin=function(){var e,t,i=this,n=i.constructor;return i.isFinite()?i.isZero()?new n(i):(e=n.precision,t=n.rounding,n.precision=e+Math.max(i.e,i.sd())+7,n.rounding=1,i=function(e,t){var i,n=t.d.length;if(n<3)return t.isZero()?t:i5(e,2,t,t);i=(i=1.4*Math.sqrt(n))>16?16:0|i,t=i5(e,2,t=t.times(1/i8(5,i)),t);for(var r,s=new e(5),a=new e(16),o=new e(20);i--;)r=t.times(t),t=t.times(s.plus(r.times(a.times(r).minus(o))));return t}(n,ne(n,i)),n.precision=e,n.rounding=t,iW(iP>2?i.neg():i,e,t,!0)):new n(NaN)},iM.squareRoot=iM.sqrt=function(){var e,t,i,n,r,s,a=this.d,o=this.e,l=this.s,u=this.constructor;if(1!==l||!a||!a[0])return new u(!l||l<0&&(!a||a[0])?NaN:a?this:1/0);for(iT=!1,0==(l=Math.sqrt(+this))||l==1/0?(((t=iG(a)).length+o)%2==0&&(t+="0"),l=Math.sqrt(t),o=iN((o+1)/2)-(o<0||o%2),n=new u(t=l==1/0?"5e"+o:(t=l.toExponential()).slice(0,t.indexOf("e")+1)+o)):n=new u(l.toString()),i=(o=u.precision)+3;;)if(n=(s=n).plus(iJ(this,s,i+2,1)).times(.5),iG(s.d).slice(0,i)===(t=iG(n.d)).slice(0,i)){if("9999"!=(t=t.slice(i-3,i+1))&&(r||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(iW(n,o+1,1),e=!n.times(n).eq(this));break}if(!r&&(iW(s,o+1,0),s.times(s).eq(this))){n=s;break}i+=4,r=1}return iT=!0,iW(n,o,u.rounding,e)},iM.tangent=iM.tan=function(){var e,t,i=this,n=i.constructor;return i.isFinite()?i.isZero()?new n(i):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,(i=i.sin()).s=1,i=iJ(i,new n(1).minus(i.times(i)).sqrt(),e+10,0),n.precision=e,n.rounding=t,iW(2==iP||4==iP?i.neg():i,e,t,!0)):new n(NaN)},iM.times=iM.mul=function(e){var t,i,n,r,s,a,o,l,u,d=this.constructor,c=this.d,f=(e=new d(e)).d;if(e.s*=this.s,!c||!c[0]||!f||!f[0])return new d(e.s&&(!c||c[0]||f)&&(!f||f[0]||c)?c&&f?0*e.s:e.s/0:NaN);for(i=iN(this.e/7)+iN(e.e/7),(l=c.length)<(u=f.length)&&(s=c,c=f,f=s,a=l,l=u,u=a),s=[],n=a=l+u;n--;)s.push(0);for(n=u;--n>=0;){for(t=0,r=l+n;r>n;)o=s[r]+f[n]*c[r-n-1]+t,s[r--]=o%1e7|0,t=o/1e7|0;s[r]=(s[r]+t)%1e7|0}for(;!s[--a];)s.pop();return t?++i:s.shift(),e.d=s,e.e=iK(s,i),iT?iW(e,d.precision,d.rounding):e},iM.toBinary=function(e,t){return nt(this,2,e,t)},iM.toDecimalPlaces=iM.toDP=function(e,t){var i=this,n=i.constructor;return i=new n(i),void 0===e?i:(iB(e,0,1e9),void 0===t?t=n.rounding:iB(t,0,8),iW(i,e+i.e+1,t))},iM.toExponential=function(e,t){var i,n=this,r=n.constructor;return void 0===e?i=iH(n,!0):(iB(e,0,1e9),void 0===t?t=r.rounding:iB(t,0,8),i=iH(n=iW(new r(n),e+1,t),!0,e+1)),n.isNeg()&&!n.isZero()?"-"+i:i},iM.toFixed=function(e,t){var i,n,r=this.constructor;return void 0===e?i=iH(this):(iB(e,0,1e9),void 0===t?t=r.rounding:iB(t,0,8),i=iH(n=iW(new r(this),e+this.e+1,t),!1,e+n.e+1)),this.isNeg()&&!this.isZero()?"-"+i:i},iM.toFraction=function(e){var t,i,n,r,s,a,o,l,u,d,c,f,h=this.d,p=this.constructor;if(!h)return new p(this);if(u=i=new p(1),n=l=new p(0),a=(s=(t=new p(n)).e=iX(h)-this.e-1)%7,t.d[0]=iD(10,a<0?7+a:a),null==e)e=s>0?t:u;else{if(!(o=new p(e)).isInt()||o.lt(u))throw Error(iq+o);e=o.gt(t)?s>0?t:u:o}for(iT=!1,o=new p(iG(h)),d=p.precision,p.precision=s=14*h.length;c=iJ(o,t,0,1,1),1!=(r=i.plus(c.times(n))).cmp(e);)i=n,n=r,r=u,u=l.plus(c.times(r)),l=r,r=t,t=o.minus(c.times(r)),o=r;return r=iJ(e.minus(i),n,0,1,1),l=l.plus(r.times(u)),i=i.plus(r.times(n)),l.s=u.s=this.s,f=1>iJ(u,n,s,1).minus(this).abs().cmp(iJ(l,i,s,1).minus(this).abs())?[u,n]:[l,i],p.precision=d,iT=!0,f},iM.toHexadecimal=iM.toHex=function(e,t){return nt(this,16,e,t)},iM.toNearest=function(e,t){var i=this,n=i.constructor;if(i=new n(i),null==e){if(!i.d)return i;e=new n(1),t=n.rounding}else{if(e=new n(e),void 0===t?t=n.rounding:iB(t,0,8),!i.d)return e.s?i:e;if(!e.d)return e.s&&(e.s=i.s),e}return e.d[0]?(iT=!1,i=iJ(i,e,0,t,1).times(e),iT=!0,iW(i)):(e.s=i.s,i=e),i},iM.toNumber=function(){return+this},iM.toOctal=function(e,t){return nt(this,8,e,t)},iM.toPower=iM.pow=function(e){var t,i,n,r,s,a,o=this,l=o.constructor,u=+(e=new l(e));if(!o.d||!e.d||!o.d[0]||!e.d[0])return new l(iD(+o,u));if((o=new l(o)).eq(1))return o;if(n=l.precision,s=l.rounding,e.eq(1))return iW(o,n,s);if((t=iN(e.e/7))>=e.d.length-1&&(i=u<0?-u:u)<=0x1fffffffffffff)return r=i1(l,o,i,n),e.s<0?new l(1).div(r):iW(r,n,s);if((a=o.s)<0){if(t<e.d.length-1)return new l(NaN);if((1&e.d[t])==0&&(a=1),0==o.e&&1==o.d[0]&&1==o.d.length)return o.s=a,o}return(t=0!=(i=iD(+o,u))&&isFinite(i)?new l(i+"").e:iN(u*(Math.log("0."+iG(o.d))/Math.LN10+o.e+1)))>l.maxE+1||t<l.minE-1?new l(t>0?a/0:0):(iT=!1,l.rounding=o.s=1,i=Math.min(12,(t+"").length),(r=i3(e.times(i6(o,n+i)),n)).d&&iQ((r=iW(r,n+5,1)).d,n,s)&&(t=n+10,+iG((r=iW(i3(e.times(i6(o,t+i)),t),t+5,1)).d).slice(n+1,n+15)+1==1e14&&(r=iW(r,n+1,0))),r.s=a,iT=!0,l.rounding=s,iW(r,n,s))},iM.toPrecision=function(e,t){var i,n=this,r=n.constructor;return void 0===e?i=iH(n,n.e<=r.toExpNeg||n.e>=r.toExpPos):(iB(e,1,1e9),void 0===t?t=r.rounding:iB(t,0,8),i=iH(n=iW(new r(n),e,t),e<=n.e||n.e<=r.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+i:i},iM.toSignificantDigits=iM.toSD=function(e,t){var i=this.constructor;return void 0===e?(e=i.precision,t=i.rounding):(iB(e,1,1e9),void 0===t?t=i.rounding:iB(t,0,8)),iW(new i(this),e,t)},iM.toString=function(){var e=this.constructor,t=iH(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()&&!this.isZero()?"-"+t:t},iM.truncated=iM.trunc=function(){return iW(new this.constructor(this),this.e+1,1)},iM.valueOf=iM.toJSON=function(){var e=this.constructor,t=iH(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()?"-"+t:t};var iJ=function(){function e(e,t,i){var n,r=0,s=e.length;for(e=e.slice();s--;)n=e[s]*t+r,e[s]=n%i|0,r=n/i|0;return r&&e.unshift(r),e}function t(e,t,i,n){var r,s;if(i!=n)s=i>n?1:-1;else for(r=s=0;r<i;r++)if(e[r]!=t[r]){s=e[r]>t[r]?1:-1;break}return s}function i(e,t,i,n){for(var r=0;i--;)e[i]-=r,r=e[i]<t[i]?1:0,e[i]=r*n+e[i]-t[i];for(;!e[0]&&e.length>1;)e.shift()}return function(n,r,s,a,o,l){var u,d,c,f,h,p,m,g,y,b,w,v,E,x,P,R,_,S,A,T,O=n.constructor,q=n.s==r.s?1:-1,I=n.d,k=r.d;if(!I||!I[0]||!k||!k[0])return new O(n.s&&r.s&&(I?!k||I[0]!=k[0]:k)?I&&0==I[0]||!k?0*q:q/0:NaN);for(l?(h=1,d=n.e-r.e):(l=1e7,h=7,d=iN(n.e/h)-iN(r.e/h)),A=k.length,_=I.length,b=(y=new O(q)).d=[],c=0;k[c]==(I[c]||0);c++);if(k[c]>(I[c]||0)&&d--,null==s?(x=s=O.precision,a=O.rounding):x=o?s+(n.e-r.e)+1:s,x<0)b.push(1),p=!0;else{if(x=x/h+2|0,c=0,1==A){for(f=0,k=k[0],x++;(c<_||f)&&x--;c++)P=f*l+(I[c]||0),b[c]=P/k|0,f=P%k|0;p=f||c<_}else{for((f=l/(k[0]+1)|0)>1&&(k=e(k,f,l),I=e(I,f,l),A=k.length,_=I.length),R=A,v=(w=I.slice(0,A)).length;v<A;)w[v++]=0;(T=k.slice()).unshift(0),S=k[0],k[1]>=l/2&&++S;do f=0,(u=t(k,w,A,v))<0?(E=w[0],A!=v&&(E=E*l+(w[1]||0)),(f=E/S|0)>1?(f>=l&&(f=l-1),g=(m=e(k,f,l)).length,v=w.length,1==(u=t(m,w,g,v))&&(f--,i(m,A<g?T:k,g,l))):(0==f&&(u=f=1),m=k.slice()),(g=m.length)<v&&m.unshift(0),i(w,m,v,l),-1==u&&(v=w.length,(u=t(k,w,A,v))<1&&(f++,i(w,A<v?T:k,v,l))),v=w.length):0===u&&(f++,w=[0]),b[c++]=f,u&&w[0]?w[v++]=I[R]||0:(w=[I[R]],v=1);while((R++<_||void 0!==w[0])&&x--);p=void 0!==w[0]}b[0]||b.shift()}if(1==h)y.e=d,ix=p;else{for(c=1,f=b[0];f>=10;f/=10)c++;y.e=c+d*h-1,iW(y,o?s+y.e+1:s,a,p)}return y}}();function iW(e,t,i,n){var r,s,a,o,l,u,d,c,f,h=e.constructor;e:if(null!=t){if(!(c=e.d))return e;for(r=1,o=c[0];o>=10;o/=10)r++;if((s=t-r)<0)s+=7,a=t,l=(d=c[f=0])/iD(10,r-a-1)%10|0;else if((f=Math.ceil((s+1)/7))>=(o=c.length)){if(n){for(;o++<=f;)c.push(0);d=l=0,r=1,s%=7,a=s-7+1}else break e}else{for(d=o=c[f],r=1;o>=10;o/=10)r++;s%=7,l=(a=s-7+r)<0?0:d/iD(10,r-a-1)%10|0}if(n=n||t<0||void 0!==c[f+1]||(a<0?d:d%iD(10,r-a-1)),u=i<4?(l||n)&&(0==i||i==(e.s<0?3:2)):l>5||5==l&&(4==i||n||6==i&&(s>0?a>0?d/iD(10,r-a):0:c[f-1])%10&1||i==(e.s<0?8:7)),t<1||!c[0])return c.length=0,u?(t-=e.e+1,c[0]=iD(10,(7-t%7)%7),e.e=-t||0):c[0]=e.e=0,e;if(0==s?(c.length=f,o=1,f--):(c.length=f+1,o=iD(10,7-s),c[f]=a>0?(d/iD(10,r-a)%iD(10,a)|0)*o:0),u)for(;;)if(0==f){for(s=1,a=c[0];a>=10;a/=10)s++;for(a=c[0]+=o,o=1;a>=10;a/=10)o++;s!=o&&(e.e++,1e7==c[0]&&(c[0]=1));break}else{if(c[f]+=o,1e7!=c[f])break;c[f--]=0,o=1}for(s=c.length;0===c[--s];)c.pop()}return iT&&(e.e>h.maxE?(e.d=null,e.e=NaN):e.e<h.minE&&(e.e=0,e.d=[0])),e}function iH(e,t,i){if(!e.isFinite())return i7(e);var n,r=e.e,s=iG(e.d),a=s.length;return t?(i&&(n=i-a)>0?s=s.charAt(0)+"."+s.slice(1)+i0(n):a>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(e.e<0?"e":"e+")+e.e):r<0?(s="0."+i0(-r-1)+s,i&&(n=i-a)>0&&(s+=i0(n))):r>=a?(s+=i0(r+1-a),i&&(n=i-r-1)>0&&(s=s+"."+i0(n))):((n=r+1)<a&&(s=s.slice(0,n)+"."+s.slice(n)),i&&(n=i-a)>0&&(r+1===a&&(s+="."),s+=i0(n))),s}function iK(e,t){var i=e[0];for(t*=7;i>=10;i/=10)t++;return t}function iY(e,t,i){if(t>iC)throw iT=!0,i&&(e.precision=i),Error(iI);return iW(new e(i_),t,1,!0)}function iZ(e,t,i){if(t>iL)throw Error(iI);return iW(new e(iS),t,i,!0)}function iX(e){var t=e.length-1,i=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)i--;for(t=e[0];t>=10;t/=10)i++}return i}function i0(e){for(var t="";e--;)t+="0";return t}function i1(e,t,i,n){var r,s=new e(1),a=Math.ceil(n/7+4);for(iT=!1;;){if(i%2&&ni((s=s.times(t)).d,a)&&(r=!0),0===(i=iN(i/2))){i=s.d.length-1,r&&0===s.d[i]&&++s.d[i];break}ni((t=t.times(t)).d,a)}return iT=!0,s}function i2(e){return 1&e.d[e.d.length-1]}function i4(e,t,i){for(var n,r,s=new e(t[0]),a=0;++a<t.length;){if(!(r=new e(t[a])).s){s=r;break}((n=s.cmp(r))===i||0===n&&s.s===i)&&(s=r)}return s}function i3(e,t){var i,n,r,s,a,o,l,u=0,d=0,c=0,f=e.constructor,h=f.rounding,p=f.precision;if(!e.d||!e.d[0]||e.e>17)return new f(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(iT=!1,l=p):l=t,o=new f(.03125);e.e>-2;)e=e.times(o),c+=5;for(l+=n=Math.log(iD(2,c))/Math.LN10*2+5|0,i=s=a=new f(1),f.precision=l;;){if(s=iW(s.times(e),l,1),i=i.times(++d),iG((o=a.plus(iJ(s,i,l,1))).d).slice(0,l)===iG(a.d).slice(0,l)){for(r=c;r--;)a=iW(a.times(a),l,1);if(null!=t)return f.precision=p,a;if(!(u<3&&iQ(a.d,l-n,h,u)))return iW(a,f.precision=p,h,iT=!0);f.precision=l+=10,i=s=o=new f(1),d=0,u++}a=o}}function i6(e,t){var i,n,r,s,a,o,l,u,d,c,f,h=1,p=e,m=p.d,g=p.constructor,y=g.rounding,b=g.precision;if(p.s<0||!m||!m[0]||!p.e&&1==m[0]&&1==m.length)return new g(m&&!m[0]?-1/0:1!=p.s?NaN:m?0:p);if(null==t?(iT=!1,d=b):d=t,g.precision=d+=10,n=(i=iG(m)).charAt(0),!(15e14>Math.abs(s=p.e)))return u=iY(g,d+2,b).times(s+""),p=i6(new g(n+"."+i.slice(1)),d-10).plus(u),g.precision=b,null==t?iW(p,b,y,iT=!0):p;for(;n<7&&1!=n||1==n&&i.charAt(1)>3;)n=(i=iG((p=p.times(e)).d)).charAt(0),h++;for(s=p.e,n>1?(p=new g("0."+i),s++):p=new g(n+"."+i.slice(1)),c=p,l=a=p=iJ(p.minus(1),p.plus(1),d,1),f=iW(p.times(p),d,1),r=3;;){if(a=iW(a.times(f),d,1),iG((u=l.plus(iJ(a,new g(r),d,1))).d).slice(0,d)===iG(l.d).slice(0,d)){if(l=l.times(2),0!==s&&(l=l.plus(iY(g,d+2,b).times(s+""))),l=iJ(l,new g(h),d,1),null!=t)return g.precision=b,l;if(!iQ(l.d,d-10,y,o))return iW(l,g.precision=b,y,iT=!0);g.precision=d+=10,u=a=p=iJ(c.minus(1),c.plus(1),d,1),f=iW(p.times(p),d,1),r=o=1}l=u,r+=2}}function i7(e){return String(e.s*e.s/0)}function i9(e,t){var i,n,r;for((i=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(i<0&&(i=n),i+=+t.slice(n+1),t=t.substring(0,n)):i<0&&(i=t.length),n=0;48===t.charCodeAt(n);n++);for(r=t.length;48===t.charCodeAt(r-1);--r);if(t=t.slice(n,r)){if(r-=n,e.e=i=i-n-1,e.d=[],n=(i+1)%7,i<0&&(n+=7),n<r){for(n&&e.d.push(+t.slice(0,n)),r-=7;n<r;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=r;for(;n--;)t+="0";e.d.push(+t),iT&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function i5(e,t,i,n,r){var s,a,o,l,u=e.precision,d=Math.ceil(u/7);for(iT=!1,l=i.times(i),o=new e(n);;){if(a=iJ(o.times(l),new e(t++*t++),u,1),o=r?n.plus(a):n.minus(a),n=iJ(a.times(l),new e(t++*t++),u,1),void 0!==(a=o.plus(n)).d[d]){for(s=d;a.d[s]===o.d[s]&&s--;);if(-1==s)break}s=o,o=n,n=a,a=s}return iT=!0,a.d.length=d+1,a}function i8(e,t){for(var i=e;--t;)i*=e;return i}function ne(e,t){var i,n=t.s<0,r=iZ(e,e.precision,1),s=r.times(.5);if((t=t.abs()).lte(s))return iP=n?4:1,t;if((i=t.divToInt(r)).isZero())iP=n?3:2;else{if((t=t.minus(i.times(r))).lte(s))return iP=i2(i)?n?2:3:n?4:1,t;iP=i2(i)?n?1:4:n?3:2}return t.minus(r).abs()}function nt(e,t,i,n){var r,s,a,o,l,u,d,c,f,h=e.constructor,p=void 0!==i;if(p?(iB(i,1,1e9),void 0===n?n=h.rounding:iB(n,0,8)):(i=h.precision,n=h.rounding),e.isFinite()){for(a=(d=iH(e)).indexOf("."),p?(r=2,16==t?i=4*i-3:8==t&&(i=3*i-2)):r=t,a>=0&&(d=d.replace(".",""),(f=new h(1)).e=d.length-a,f.d=iz(iH(f),10,r),f.e=f.d.length),s=l=(c=iz(d,10,r)).length;0==c[--l];)c.pop();if(c[0]){if(a<0?s--:((e=new h(e)).d=c,e.e=s,c=(e=iJ(e,f,i,n,0,r)).d,s=e.e,u=ix),a=c[i],o=r/2,u=u||void 0!==c[i+1],u=n<4?(void 0!==a||u)&&(0===n||n===(e.s<0?3:2)):a>o||a===o&&(4===n||u||6===n&&1&c[i-1]||n===(e.s<0?8:7)),c.length=i,u)for(;++c[--i]>r-1;)c[i]=0,i||(++s,c.unshift(1));for(l=c.length;!c[l-1];--l);for(a=0,d="";a<l;a++)d+=iR.charAt(c[a]);if(p){if(l>1){if(16==t||8==t){for(a=16==t?4:3,--l;l%a;l++)d+="0";for(l=(c=iz(d,r,t)).length;!c[l-1];--l);for(a=1,d="1.";a<l;a++)d+=iR.charAt(c[a])}else d=d.charAt(0)+"."+d.slice(1)}d=d+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)d="0"+d;d="0."+d}else if(++s>l)for(s-=l;s--;)d+="0";else s<l&&(d=d.slice(0,s)+"."+d.slice(s))}else d=p?"0p+0":"0";d=(16==t?"0x":2==t?"0b":8==t?"0o":"")+d}else d=i7(e);return e.s<0?"-"+d:d}function ni(e,t){if(e.length>t)return e.length=t,!0}function nn(e){return new this(e).abs()}function nr(e){return new this(e).acos()}function ns(e){return new this(e).acosh()}function na(e,t){return new this(e).plus(t)}function no(e){return new this(e).asin()}function nl(e){return new this(e).asinh()}function nu(e){return new this(e).atan()}function nd(e){return new this(e).atanh()}function nc(e,t){e=new this(e),t=new this(t);var i,n=this.precision,r=this.rounding,s=n+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(i=t.s<0?iZ(this,n,r):new this(0)).s=e.s:!e.d||t.isZero()?(i=iZ(this,s,1).times(.5)).s=e.s:t.s<0?(this.precision=s,this.rounding=1,i=this.atan(iJ(e,t,s,1)),t=iZ(this,s,1),this.precision=n,this.rounding=r,i=e.s<0?i.minus(t):i.plus(t)):i=this.atan(iJ(e,t,s,1)):(i=iZ(this,s,1).times(t.s>0?.25:.75)).s=e.s:i=new this(NaN),i}function nf(e){return new this(e).cbrt()}function nh(e){return iW(e=new this(e),e.e+1,2)}function np(e,t,i){return new this(e).clamp(t,i)}function nm(e){if(!e||"object"!=typeof e)throw Error(iO+"Object expected");var t,i,n,r=!0===e.defaults,s=["precision",1,1e9,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,9e15,"maxE",0,9e15,"minE",-9e15,0,"modulo",0,9];for(t=0;t<s.length;t+=3)if(i=s[t],r&&(this[i]=iA[i]),void 0!==(n=e[i])){if(iN(n)===n&&n>=s[t+1]&&n<=s[t+2])this[i]=n;else throw Error(iq+i+": "+n)}if(i="crypto",r&&(this[i]=iA[i]),void 0!==(n=e[i])){if(!0===n||!1===n||0===n||1===n){if(n){if("u">typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[i]=!0;else throw Error(ik)}else this[i]=!1}else throw Error(iq+i+": "+n)}return this}function ng(e){return new this(e).cos()}function ny(e){return new this(e).cosh()}function nb(e,t){return new this(e).div(t)}function nw(e){return new this(e).exp()}function nv(e){return iW(e=new this(e),e.e+1,3)}function nE(){var e,t,i=new this(0);for(iT=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)i.d&&(i=i.plus(t.times(t)));else{if(t.s)return iT=!0,new this(1/0);i=t}return iT=!0,i.sqrt()}function nx(e){return e instanceof nG||e&&e.toStringTag===i$||!1}function nP(e){return new this(e).ln()}function nR(e,t){return new this(e).log(t)}function n_(e){return new this(e).log(2)}function nS(e){return new this(e).log(10)}function nA(){return i4(this,arguments,-1)}function nT(){return i4(this,arguments,1)}function nO(e,t){return new this(e).mod(t)}function nq(e,t){return new this(e).mul(t)}function nI(e,t){return new this(e).pow(t)}function nk(e){var t,i,n,r,s=0,a=new this(1),o=[];if(void 0===e?e=this.precision:iB(e,1,1e9),n=Math.ceil(e/7),this.crypto){if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));s<n;)(r=t[s])>=429e7?t[s]=crypto.getRandomValues(new Uint32Array(1))[0]:o[s++]=r%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);s<n;)(r=t[s]+(t[s+1]<<8)+(t[s+2]<<16)+((127&t[s+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,s):(o.push(r%1e7),s+=4);s=n/4}else throw Error(ik)}else for(;s<n;)o[s++]=1e7*Math.random()|0;for(n=o[--s],e%=7,n&&e&&(r=iD(10,7-e),o[s]=(n/r|0)*r);0===o[s];s--)o.pop();if(s<0)i=0,o=[0];else{for(i=-1;0===o[0];i-=7)o.shift();for(n=1,r=o[0];r>=10;r/=10)n++;n<7&&(i-=7-n)}return a.e=i,a.d=o,a}function n$(e){return iW(e=new this(e),e.e+1,this.rounding)}function nN(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function nD(e){return new this(e).sin()}function nF(e){return new this(e).sinh()}function nU(e){return new this(e).sqrt()}function nV(e,t){return new this(e).sub(t)}function nj(){var e=0,t=arguments,i=new this(t[0]);for(iT=!1;i.s&&++e<t.length;)i=i.plus(t[e]);return iT=!0,iW(i,this.precision,this.rounding)}function nC(e){return new this(e).tan()}function nL(e){return new this(e).tanh()}function nM(e){return iW(e=new this(e),e.e+1,1)}iM[Symbol.for("nodejs.util.inspect.custom")]=iM.toString,iM[Symbol.toStringTag]="Decimal";var nG=iM.constructor=function e(t){var i,n,r;function s(e){var t,i,n;if(!(this instanceof s))return new s(e);if(this.constructor=s,nx(e)){this.s=e.s,iT?!e.d||e.e>s.maxE?(this.e=NaN,this.d=null):e.e<s.minE?(this.e=0,this.d=[0]):(this.e=e.e,this.d=e.d.slice()):(this.e=e.e,this.d=e.d?e.d.slice():e.d);return}if("number"==(n=typeof e)){if(0===e){this.s=1/e<0?-1:1,this.e=0,this.d=[0];return}if(e<0?(e=-e,this.s=-1):this.s=1,e===~~e&&e<1e7){for(t=0,i=e;i>=10;i/=10)t++;iT?t>s.maxE?(this.e=NaN,this.d=null):t<s.minE?(this.e=0,this.d=[0]):(this.e=t,this.d=[e]):(this.e=t,this.d=[e]);return}if(0*e!=0){e||(this.s=NaN),this.e=NaN,this.d=null;return}return i9(this,e.toString())}if("string"===n)return 45===(i=e.charCodeAt(0))?(e=e.slice(1),this.s=-1):(43===i&&(e=e.slice(1)),this.s=1),ij.test(e)?i9(this,e):function(e,t){var i,n,r,s,a,o,l,u,d;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),ij.test(t))return i9(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(iU.test(t))i=16,t=t.toLowerCase();else if(iF.test(t))i=2;else if(iV.test(t))i=8;else throw Error(iq+t);for((s=t.search(/p/i))>0?(l=+t.slice(s+1),t=t.substring(2,s)):t=t.slice(2),a=(s=t.indexOf("."))>=0,n=e.constructor,a&&(s=(o=(t=t.replace(".","")).length)-s,r=i1(n,new n(i),s,2*s)),s=d=(u=iz(t,i,1e7)).length-1;0===u[s];--s)u.pop();return s<0?new n(0*e.s):(e.e=iK(u,d),e.d=u,iT=!1,a&&(e=iJ(e,r,4*o)),l&&(e=e.times(54>Math.abs(l)?iD(2,l):nG.pow(2,l))),iT=!0,e)}(this,e);if("bigint"===n)return e<0?(e=-e,this.s=-1):this.s=1,i9(this,e.toString());throw Error(iq+e)}if(s.prototype=iM,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=nm,s.clone=e,s.isDecimal=nx,s.abs=nn,s.acos=nr,s.acosh=ns,s.add=na,s.asin=no,s.asinh=nl,s.atan=nu,s.atanh=nd,s.atan2=nc,s.cbrt=nf,s.ceil=nh,s.clamp=np,s.cos=ng,s.cosh=ny,s.div=nb,s.exp=nw,s.floor=nv,s.hypot=nE,s.ln=nP,s.log=nR,s.log10=nS,s.log2=n_,s.max=nA,s.min=nT,s.mod=nO,s.mul=nq,s.pow=nI,s.random=nk,s.round=n$,s.sign=nN,s.sin=nD,s.sinh=nF,s.sqrt=nU,s.sub=nV,s.sum=nj,s.tan=nC,s.tanh=nL,s.trunc=nM,void 0===t&&(t={}),t&&!0!==t.defaults)for(r=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],i=0;i<r.length;)t.hasOwnProperty(n=r[i++])||(t[n]=this[n]);return s.config(t),s}(iA);i_=new nG(i_),iS=new nG(iS);var nB=nG;function nQ(e){return null===e?e:Array.isArray(e)?e.map(nQ):"object"==typeof e?null!==e&&"object"==typeof e&&"string"==typeof e.$type?function({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:e,byteOffset:i,byteLength:n}=Buffer.from(t,"base64");return new Uint8Array(e,i,n)}case"DateTime":return new Date(t);case"Decimal":return new nB(t);case"Json":return JSON.parse(t);default:ii(t,"Unknown tagged value")}}(e):"bigint"==typeof e||e instanceof Date||e instanceof Uint8Array||e instanceof nB?e:ih(e,nQ):e}var nz=class{get(e){return this._map.get(e)?.value}set(e,t){this._map.set(e,{value:t})}getOrCreate(e,t){let i=this._map.get(e);if(i)return i.value;let n=t();return this.set(e,n),n}constructor(){this._map=new Map}};function nJ(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function nW(e){let t;return{get:()=>(t||(t={value:e()}),t.value)}}function nH(e){return{models:nK(e.models),enums:nK(e.enums),types:nK(e.types)}}function nK(e){let t={};for(let{name:i,...n}of e)t[i]=n;return t}function nY(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function nZ(e){return"Invalid Date"!==e.toString()}function nX(e){return!!nG.isDecimal(e)||null!==e&&"object"==typeof e&&"number"==typeof e.s&&"number"==typeof e.e&&"function"==typeof e.toFixed&&Array.isArray(e.d)}var n0={};function n1(e){return{name:e.name,values:e.values.map(e=>e.name)}}c(n0,{ModelAction:()=>n2,datamodelEnumToSchemaEnum:()=>n1});var n2=(e=>(e.findUnique="findUnique",e.findUniqueOrThrow="findUniqueOrThrow",e.findFirst="findFirst",e.findFirstOrThrow="findFirstOrThrow",e.findMany="findMany",e.create="create",e.createMany="createMany",e.createManyAndReturn="createManyAndReturn",e.update="update",e.updateMany="updateMany",e.updateManyAndReturn="updateManyAndReturn",e.upsert="upsert",e.delete="delete",e.deleteMany="deleteMany",e.groupBy="groupBy",e.count="count",e.aggregate="aggregate",e.findRaw="findRaw",e.aggregateRaw="aggregateRaw",e))(n2||{});h(E()),h(i(73024));var n4={keyword:er,entity:er,value:e=>Q(ei(e)),punctuation:ei,directive:er,function:er,variable:e=>Q(ei(e)),string:e=>Q(ee(e)),boolean:et,number:er,comment:ea},n3=e=>e,n6={},n7=0,n9={manual:n6.Prism&&n6.Prism.manual,disableWorkerMessageHandler:n6.Prism&&n6.Prism.disableWorkerMessageHandler,util:{encode:function(e){return e instanceof n5?new n5(e.type,n9.util.encode(e.content),e.alias):Array.isArray(e)?e.map(n9.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++n7}),e.__id},clone:function e(t,i){let n,r,s=n9.util.type(t);switch(i=i||{},s){case"Object":if(i[r=n9.util.objId(t)])return i[r];for(let s in n={},i[r]=n,t)t.hasOwnProperty(s)&&(n[s]=e(t[s],i));return n;case"Array":return i[r=n9.util.objId(t)]?i[r]:(n=[],i[r]=n,t.forEach(function(t,r){n[r]=e(t,i)}),n);default:return t}}},languages:{extend:function(e,t){let i=n9.util.clone(n9.languages[e]);for(let e in t)i[e]=t[e];return i},insertBefore:function(e,t,i,n){let r=(n=n||n9.languages)[e],s={};for(let e in r)if(r.hasOwnProperty(e)){if(e==t)for(let e in i)i.hasOwnProperty(e)&&(s[e]=i[e]);i.hasOwnProperty(e)||(s[e]=r[e])}let a=n[e];return n[e]=s,n9.languages.DFS(n9.languages,function(t,i){i===a&&t!=e&&(this[t]=s)}),s},DFS:function e(t,i,n,r){r=r||{};let s=n9.util.objId;for(let a in t)if(t.hasOwnProperty(a)){i.call(t,a,t[a],n||a);let o=t[a],l=n9.util.type(o);"Object"!==l||r[s(o)]?"Array"!==l||r[s(o)]||(r[s(o)]=!0,e(o,i,a,r)):(r[s(o)]=!0,e(o,i,null,r))}}},plugins:{},highlight:function(e,t,i){let n={code:e,grammar:t,language:i};return n9.hooks.run("before-tokenize",n),n.tokens=n9.tokenize(n.code,n.grammar),n9.hooks.run("after-tokenize",n),n5.stringify(n9.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,i,n,r,s,a){for(let m in i){if(!i.hasOwnProperty(m)||!i[m])continue;if(m==a)return;let g=i[m];g="Array"===n9.util.type(g)?g:[g];for(let a=0;a<g.length;++a){let y=g[a],b=y.inside,w=!!y.lookbehind,v=!!y.greedy,E=0,x=y.alias;if(v&&!y.pattern.global){let e=y.pattern.toString().match(/[imuy]*$/)[0];y.pattern=RegExp(y.pattern.source,e+"g")}y=y.pattern||y;for(let a=n,g=r;a<t.length;g+=t[a].length,++a){let n=t[a];if(t.length>e.length)return;if(n instanceof n5)continue;if(v&&a!=t.length-1){y.lastIndex=g;var o=y.exec(e);if(!o)break;var l=o.index+(w?o[1].length:0),u=o.index+o[0].length,d=a,c=g;for(let e=t.length;d<e&&(c<u||!t[d].type&&!t[d-1].greedy);++d)l>=(c+=t[d].length)&&(++a,g=c);if(t[a]instanceof n5)continue;f=d-a,n=e.slice(g,c),o.index-=g}else{y.lastIndex=0;var o=y.exec(n),f=1}if(!o){if(s)break;continue}w&&(E=o[1]?o[1].length:0);var l=o.index+E,o=o[0].slice(E),u=l+o.length,h=n.slice(0,l),p=n.slice(u);let r=[a,f];h&&(++a,g+=h.length,r.push(h));let P=new n5(m,b?n9.tokenize(o,b):o,x,o,v);if(r.push(P),p&&r.push(p),Array.prototype.splice.apply(t,r),1!=f&&n9.matchGrammar(e,t,i,a,g,!0,m),s)break}}}},tokenize:function(e,t){let i=[e],n=t.rest;if(n){for(let e in n)t[e]=n[e];delete t.rest}return n9.matchGrammar(e,i,t,0,0,!1),i},hooks:{all:{},add:function(e,t){let i=n9.hooks.all;i[e]=i[e]||[],i[e].push(t)},run:function(e,t){let i=n9.hooks.all[e];if(!(!i||!i.length))for(var n,r=0;n=i[r++];)n(t)}},Token:n5};function n5(e,t,i,n,r){this.type=e,this.content=t,this.alias=i,this.length=0|(n||"").length,this.greedy=!!r}n9.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/},n9.languages.javascript=n9.languages.extend("clike",{"class-name":[n9.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/}),n9.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/,n9.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:n9.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:n9.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:n9.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:n9.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),n9.languages.markup&&n9.languages.markup.tag.addInlined("script","javascript"),n9.languages.js=n9.languages.javascript,n9.languages.typescript=n9.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/}),n9.languages.ts=n9.languages.typescript,n5.stringify=function(e,t){return"string"==typeof e?e:Array.isArray(e)?e.map(function(e){return n5.stringify(e,t)}).join(""):(n4[e.type]||n3)(e.content)};var n8={red:X,gray:ea,dim:z,bold:Q,underline:W,highlightSource:e=>e.highlight()},re={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function rt(e){let t=e.showColors?n8:re;return function({functionName:e,location:t,message:i,isPanic:n,contextLines:r,callArguments:s},a){let o,l=[""],u=t?" in":":";if(n?(l.push(a.red(`Oops, an unknown error occurred! This is ${a.bold("on us")}, you did nothing wrong.`)),l.push(a.red(`It occurred in the ${a.bold(`\`${e}\``)} invocation${u}`))):l.push(a.red(`Invalid ${a.bold(`\`${e}\``)} invocation${u}`)),t&&l.push(a.underline((o=[t.fileName],t.lineNumber&&o.push(String(t.lineNumber)),t.columnNumber&&o.push(String(t.columnNumber)),o.join(":")))),r){l.push("");let e=[r.toString()];s&&(e.push(s),e.push(a.dim(")"))),l.push(e.join("")),s&&l.push("")}else l.push(""),s&&l.push(s),l.push("");return l.push(i),l.join(`
`)}(function({callsite:e,message:t,originalMethod:i,isPanic:n,callArguments:r},s){return function({message:e,originalMethod:t,isPanic:i,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:i??!1,callArguments:n}}({message:t,originalMethod:i,isPanic:n,callArguments:r})}(e,0),t)}var ri=h(A());function rn(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function rr(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return -10;default:return 0}}var rs=class{constructor(e,t){this.isRequired=!1,this.name=e,this.value=t}makeRequired(){return this.isRequired=!0,this}write(e){let{colors:{green:t}}=e.context;e.addMarginSymbol(t(this.isRequired?"+":"?")),e.write(t(this.name)),this.isRequired||e.write(t("?")),e.write(t(": ")),"string"==typeof this.value?e.write(t(this.value)):e.write(this.value)}};O();var ra=class{constructor(e=0,t){this.lines=[],this.currentLine="",this.currentIndent=0,this.context=t,this.currentIndent=e}write(e){return"string"==typeof e?this.currentLine+=e:e.write(this),this}writeJoined(e,t,i=(e,t)=>t.write(e)){let n=t.length-1;for(let r=0;r<t.length;r++)i(t[r],this),r!==n&&this.write(e);return this}writeLine(e){return this.write(e).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let e=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,e?.(),this}withIndent(e){return this.indent(),e(this),this.unindent(),this}afterNextNewline(e){return this.afterNextNewLineCallback=e,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(e){return this.marginSymbol=e,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let e=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+e.slice(1):e}};T();var ro=class{constructor(e){this.value=e}write(e){e.write(this.value)}markAsError(){this.value.markAsError()}},rl=e=>e,ru={bold:rl,red:rl,green:rl,dim:rl,enabled:!1},rd={bold:Q,red:X,green:ee,dim:z,enabled:!0},rc={write(e){e.writeLine(",")}},rf=class{constructor(e){this.isUnderlined=!1,this.color=e=>e,this.contents=e}underline(){return this.isUnderlined=!0,this}setColor(e){return this.color=e,this}write(e){let t=e.getCurrentLineLength();e.write(this.color(this.contents)),this.isUnderlined&&e.afterNextNewline(()=>{e.write(" ".repeat(t)).writeLine(this.color("~".repeat(this.contents.length)))})}},rh=class{markAsError(){return this.hasError=!0,this}constructor(){this.hasError=!1}},rp=class extends rh{addItem(e){return this.items.push(new ro(e)),this}getField(e){return this.items[e]}getPrintWidth(){return 0===this.items.length?2:Math.max(...this.items.map(e=>e.value.getPrintWidth()))+2}write(e){if(0===this.items.length){this.writeEmpty(e);return}this.writeWithItems(e)}writeEmpty(e){let t=new rf("[]");this.hasError&&t.setColor(e.context.colors.red).underline(),e.write(t)}writeWithItems(e){let{colors:t}=e.context;e.writeLine("[").withIndent(()=>e.writeJoined(rc,this.items).newLine()).write("]"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(t.red("~".repeat(this.getPrintWidth())))})}asObject(){}constructor(...e){super(...e),this.items=[]}},rm=class e extends rh{addField(e){this.fields[e.name]=e}addSuggestion(e){this.suggestions.push(e)}getField(e){return this.fields[e]}getDeepField(t){let[i,...n]=t,r=this.getField(i);if(!r)return;let s=r;for(let t of n){let i;if(s.value instanceof e?i=s.value.getField(t):s.value instanceof rp&&(i=s.value.getField(Number(t))),!i)return;s=i}return s}getDeepFieldValue(e){return 0===e.length?this:this.getDeepField(e)?.value}hasField(e){return!!this.getField(e)}removeAllFields(){this.fields={}}removeField(e){delete this.fields[e]}getFields(){return this.fields}isEmpty(){return 0===Object.keys(this.fields).length}getFieldValue(e){return this.getField(e)?.value}getDeepSubSelectionValue(t){let i=this;for(let n of t){if(!(i instanceof e))return;let t=i.getSubSelectionValue(n);if(!t)return;i=t}return i}getDeepSelectionParent(t){let i=this.getSelectionParent();if(!i)return;let n=i;for(let i of t){let t=n.value.getFieldValue(i);if(!t||!(t instanceof e))return;let r=t.getSelectionParent();if(!r)return;n=r}return n}getSelectionParent(){let e=this.getField("select")?.value.asObject();if(e)return{kind:"select",value:e};let t=this.getField("include")?.value.asObject();if(t)return{kind:"include",value:t}}getSubSelectionValue(e){return this.getSelectionParent()?.value.fields[e].value}getPrintWidth(){let e=Object.values(this.fields);return 0==e.length?2:Math.max(...e.map(e=>e.getPrintWidth()))+2}write(e){let t=Object.values(this.fields);if(0===t.length&&0===this.suggestions.length){this.writeEmpty(e);return}this.writeWithContents(e,t)}asObject(){return this}writeEmpty(e){let t=new rf("{}");this.hasError&&t.setColor(e.context.colors.red).underline(),e.write(t)}writeWithContents(e,t){e.writeLine("{").withIndent(()=>{e.writeJoined(rc,[...t,...this.suggestions]).newLine()}),e.write("}"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(e.context.colors.red("~".repeat(this.getPrintWidth())))})}constructor(...e){super(...e),this.fields={},this.suggestions=[]}},rg=class extends rh{constructor(e){super(),this.text=e}getPrintWidth(){return this.text.length}write(e){let t=new rf(this.text);this.hasError&&t.underline().setColor(e.context.colors.red),e.write(t)}asObject(){}},ry=class{addField(e,t){return this.fields.push({write(i){let{green:n,dim:r}=i.context.colors;i.write(n(r(`${e}: ${t}`))).addMarginSymbol(n(r("+")))}}),this}write(e){let{colors:{green:t}}=e.context;e.writeLine(t("{")).withIndent(()=>{e.writeJoined(rc,this.fields).newLine()}).write(t("}")).addMarginSymbol(t("+"))}constructor(){this.fields=[]}};function rb(e,t,i){let n=[`Unknown argument \`${e.red(t)}\`.`],r=function(e,t){let i=1/0,n;for(let r of t){let t=(0,ri.default)(e,r);t>3||t<i&&(i=t,n=r)}return n}(t,i);return r&&n.push(`Did you mean \`${e.green(r)}\`?`),i.length>0&&n.push(rP(e)),n.join(" ")}function rw(e,t){for(let i of t.fields)e.hasField(i.name)||e.addSuggestion(new rs(i.name,"true"))}function rv(e,t){let[i,n]=rx(e),r=t.arguments.getDeepSubSelectionValue(i)?.asObject();if(!r)return{parentKind:"unknown",fieldName:n};let s=r.getFieldValue("select")?.asObject(),a=r.getFieldValue("include")?.asObject(),o=r.getFieldValue("omit")?.asObject(),l=s?.getField(n);return s&&l?{parentKind:"select",parent:s,field:l,fieldName:n}:(l=a?.getField(n),a&&l?{parentKind:"include",field:l,parent:a,fieldName:n}:(l=o?.getField(n),o&&l?{parentKind:"omit",field:l,parent:o,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function rE(e,t){if("object"===t.kind)for(let i of t.fields)e.hasField(i.name)||e.addSuggestion(new rs(i.name,i.typeNames.join(" | ")))}function rx(e){let t=[...e],i=t.pop();if(!i)throw Error("unexpected empty path");return[t,i]}function rP({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function rR(e,t){if(1===t.length)return t[0];let i=[...t],n=i.pop();return`${i.join(", ")} ${e} ${n}`}var r_=class{constructor(e,t,i,n,r){this.modelName=e,this.name=t,this.typeName=i,this.isList=n,this.isEnum=r}_toGraphQLInputType(){let e=this.isList?"List":"",t=this.isEnum?"Enum":"";return`${e}${t}${this.typeName}FieldRefInput<${this.modelName}>`}};function rS(e){return e instanceof r_}var rA=Symbol(),rT=new WeakMap,rO=class{constructor(e){e===rA?rT.set(this,`Prisma.${this._getName()}`):rT.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return rT.get(this)}},rq=class extends rO{_getNamespace(){return"NullTypes"}},rI=class extends rq{#e};rD(rI,"DbNull");var rk=class extends rq{#e};rD(rk,"JsonNull");var r$=class extends rq{#e};rD(r$,"AnyNull");var rN={classes:{DbNull:rI,JsonNull:rk,AnyNull:r$},instances:{DbNull:new rI(rA),JsonNull:new rk(rA),AnyNull:new r$(rA)}};function rD(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var rF=class{constructor(e,t){this.hasError=!1,this.name=e,this.value=t}markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+2}write(e){let t=new rf(this.name);this.hasError&&t.underline().setColor(e.context.colors.red),e.write(t).write(": ").write(this.value)}},rU=class{constructor(e){this.errorMessages=[],this.arguments=e}write(e){e.write(this.arguments)}addErrorMessage(e){this.errorMessages.push(e)}renderAllMessages(e){return this.errorMessages.map(t=>t(e)).join(`
`)}};function rV(e){return new rU(function e(t){let i=new rm;for(let[n,r]of Object.entries(t)){let t=new rF(n,function t(i){if("string"==typeof i)return new rg(JSON.stringify(i));if("number"==typeof i||"boolean"==typeof i)return new rg(String(i));if("bigint"==typeof i)return new rg(`${i}n`);if(null===i)return new rg("null");if(void 0===i)return new rg("undefined");if(nX(i))return new rg(`new Prisma.Decimal("${i.toFixed()}")`);if(i instanceof Uint8Array)return Buffer.isBuffer(i)?new rg(`Buffer.alloc(${i.byteLength})`):new rg(`new Uint8Array(${i.byteLength})`);if(i instanceof Date){let e=nZ(i)?i.toISOString():"Invalid Date";return new rg(`new Date("${e}")`)}return i instanceof rO?new rg(`Prisma.${i._getName()}`):rS(i)?new rg(`prisma.${nJ(i.modelName)}.$fields.${i.name}`):Array.isArray(i)?function(e){let i=new rp;for(let n of e)i.addItem(t(n));return i}(i):"object"==typeof i?e(i):new rg(Object.prototype.toString.call(i))}(r));i.addField(t)}return i}(e))}function rj(e,t){let i="pretty"===t?rd:ru;return{message:e.renderAllMessages(i),args:new ra(0,{colors:i}).write(e).toString()}}function rC({args:e,errors:t,errorFormat:i,callsite:n,originalMethod:r,clientVersion:s,globalOmit:a}){let o=rV(e);for(let e of t)(function e(t,i,n){switch(t.kind){case"MutuallyExclusiveFields":let r;(r=i.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject())&&(r.getField(t.firstField)?.markAsError(),r.getField(t.secondField)?.markAsError()),i.addErrorMessage(e=>`Please ${e.bold("either")} use ${e.green(`\`${t.firstField}\``)} or ${e.green(`\`${t.secondField}\``)}, but ${e.red("not both")} at the same time.`);break;case"IncludeOnScalar":(function(e,t){let[i,n]=rx(e.selectionPath),r=e.outputType,s=t.arguments.getDeepSelectionParent(i)?.value;if(s&&(s.getField(n)?.markAsError(),r))for(let e of r.fields)e.isRelation&&s.addSuggestion(new rs(e.name,"true"));t.addErrorMessage(e=>{let t=`Invalid scalar field ${e.red(`\`${n}\``)} for ${e.bold("include")} statement`;return r?t+=` on model ${e.bold(r.name)}. ${rP(e)}`:t+=".",t+=`
Note that ${e.bold("include")} statements only accept relation fields.`})})(t,i);break;case"EmptySelection":(function(e,t,i){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){(function(e,t,i){for(let t of(i.removeAllFields(),e.outputType.fields))i.addSuggestion(new rs(t.name,"false"));t.addErrorMessage(t=>`The ${t.red("omit")} statement includes every field of the model ${t.bold(e.outputType.name)}. At least one field must be included in the result`)})(e,t,i);return}if(n.hasField("select")){let i,n,r;i=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,r=n?.isEmpty()??!1,n&&(n.removeAllFields(),rw(n,i)),t.addErrorMessage(e=>r?`The ${e.red("`select`")} statement for type ${e.bold(i.name)} must not be empty. ${rP(e)}`:`The ${e.red("`select`")} statement for type ${e.bold(i.name)} needs ${e.bold("at least one truthy value")}.`);return}}if(i?.[nJ(e.outputType.name)]){(function(e,t){let i=new ry;for(let t of e.outputType.fields)t.isRelation||i.addField(t.name,"false");let n=new rs("omit",i).makeRequired();if(0===e.selectionPath.length)t.arguments.addSuggestion(n);else{let[i,r]=rx(e.selectionPath),s=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(r);if(s){let e=s?.value.asObject()??new rm;e.addSuggestion(n),s.value=e}}t.addErrorMessage(t=>`The global ${t.red("omit")} configuration excludes every field of the model ${t.bold(e.outputType.name)}. At least one field must be included in the result`)})(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)})(t,i,n);break;case"UnknownSelectionField":(function(e,t){let i=rv(e.selectionPath,t);if("unknown"!==i.parentKind){i.field.markAsError();let t=i.parent;switch(i.parentKind){case"select":rw(t,e.outputType);break;case"include":(function(e,t){for(let i of t.fields)i.isRelation&&!e.hasField(i.name)&&e.addSuggestion(new rs(i.name,"true"))})(t,e.outputType);break;case"omit":(function(e,t){for(let i of t.fields)e.hasField(i.name)||i.isRelation||e.addSuggestion(new rs(i.name,"true"))})(t,e.outputType)}}t.addErrorMessage(t=>{let n=[`Unknown field ${t.red(`\`${i.fieldName}\``)}`];return"unknown"!==i.parentKind&&n.push(`for ${t.bold(i.parentKind)} statement`),n.push(`on model ${t.bold(`\`${e.outputType.name}\``)}.`),n.push(rP(t)),n.join(" ")})})(t,i);break;case"InvalidSelectionValue":let s;"unknown"!==(s=rv(t.selectionPath,i)).parentKind&&s.field.value.markAsError(),i.addErrorMessage(e=>`Invalid value for selection field \`${e.red(s.fieldName)}\`: ${t.underlyingError}`);break;case"UnknownArgument":let a,o;a=t.argumentPath[0],(o=i.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject())&&(o.getField(a)?.markAsError(),function(e,t){for(let i of t)e.hasField(i.name)||e.addSuggestion(new rs(i.name,i.typeNames.join(" | ")))}(o,t.arguments)),i.addErrorMessage(e=>rb(e,a,t.arguments.map(e=>e.name)));break;case"UnknownInputField":(function(e,t){let[i,n]=rx(e.argumentPath),r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(r){r.getDeepField(e.argumentPath)?.markAsError();let t=r.getDeepFieldValue(i)?.asObject();t&&rE(t,e.inputType)}t.addErrorMessage(t=>rb(t,n,e.inputType.fields.map(e=>e.name)))})(t,i);break;case"RequiredArgumentMissing":(function(e,t){let i;t.addErrorMessage(e=>i?.value instanceof rg&&"null"===i.value.text?`Argument \`${e.green(s)}\` must not be ${e.red("null")}.`:`Argument \`${e.green(s)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[r,s]=rx(e.argumentPath),a=new ry,o=n.getDeepFieldValue(r)?.asObject();if(o){if((i=o.getField(s))&&o.removeField(s),1===e.inputTypes.length&&"object"===e.inputTypes[0].kind){for(let t of e.inputTypes[0].fields)a.addField(t.name,t.typeNames.join(" | "));o.addSuggestion(new rs(s,a).makeRequired())}else{let t=e.inputTypes.map(function e(t){return"list"===t.kind?`${e(t.elementType)}[]`:t.name}).join(" | ");o.addSuggestion(new rs(s,t).makeRequired())}}})(t,i);break;case"InvalidArgumentType":let l,u;l=t.argument.name,(u=i.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject())&&u.getDeepFieldValue(t.argumentPath)?.markAsError(),i.addErrorMessage(e=>{let i=rR("or",t.argument.typeNames.map(t=>e.green(t)));return`Argument \`${e.bold(l)}\`: Invalid value provided. Expected ${i}, provided ${e.red(t.inferredType)}.`});break;case"InvalidArgumentValue":let d,c;d=t.argument.name,(c=i.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject())&&c.getDeepFieldValue(t.argumentPath)?.markAsError(),i.addErrorMessage(e=>{let i=[`Invalid value for argument \`${e.bold(d)}\``];if(t.underlyingError&&i.push(`: ${t.underlyingError}`),i.push("."),t.argument.typeNames.length>0){let n=rR("or",t.argument.typeNames.map(t=>e.green(t)));i.push(` Expected ${n}.`)}return i.join("")});break;case"ValueTooLarge":(function(e,t){let i=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),r;if(n){let t=n.getDeepField(e.argumentPath)?.value;t?.markAsError(),t instanceof rg&&(r=t.text)}t.addErrorMessage(e=>{let t=["Unable to fit value"];return r&&t.push(e.red(r)),t.push(`into a 64-bit signed integer for field \`${e.bold(i)}\``),t.join(" ")})})(t,i);break;case"SomeFieldsMissing":(function(e,t){let i=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let t=n.getDeepFieldValue(e.argumentPath)?.asObject();t&&rE(t,e.inputType)}t.addErrorMessage(t=>{let n=[`Argument \`${t.bold(i)}\` of type ${t.bold(e.inputType.name)} needs`];return 1===e.constraints.minFieldCount?e.constraints.requiredFields?n.push(`${t.green("at least one of")} ${rR("or",e.constraints.requiredFields.map(e=>`\`${t.bold(e)}\``))} arguments.`):n.push(`${t.green("at least one")} argument.`):n.push(`${t.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),n.push(rP(t)),n.join(" ")})})(t,i);break;case"TooManyFieldsGiven":(function(e,t){let i=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),r=[];if(n){let t=n.getDeepFieldValue(e.argumentPath)?.asObject();t&&(t.markAsError(),r=Object.keys(t.getFields()))}t.addErrorMessage(t=>{let n=[`Argument \`${t.bold(i)}\` of type ${t.bold(e.inputType.name)} needs`];return 1===e.constraints.minFieldCount&&1==e.constraints.maxFieldCount?n.push(`${t.green("exactly one")} argument,`):1==e.constraints.maxFieldCount?n.push(`${t.green("at most one")} argument,`):n.push(`${t.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),n.push(`but you provided ${rR("and",r.map(e=>t.red(e)))}. Please choose`),1===e.constraints.maxFieldCount?n.push("one."):n.push(`${e.constraints.maxFieldCount}.`),n.join(" ")})})(t,i);break;case"Union":let f;(f=function(e,t){if(0===e.length)return;let i=e[0];for(let n=1;n<e.length;n++)0>t(i,e[n])&&(i=e[n]);return i}(function(e){let t=new Map,i=[];for(let s of e){var n,r;if("InvalidArgumentType"!==s.kind){i.push(s);continue}let e=`${s.selectionPath.join(".")}:${s.argumentPath.join(".")}`,a=t.get(e);a?t.set(e,{...s,argument:{...s.argument,typeNames:(n=a.argument.typeNames,r=s.argument.typeNames,[...new Set(n.concat(r))])}}):t.set(e,s)}return i.push(...t.values()),i}(function e(t){return t.errors.flatMap(t=>"Union"===t.kind?e(t):[t])}(t)),(e,t)=>{let i=rn(e),n=rn(t);return i!==n?i-n:rr(e)-rr(t)}))?e(f,i,n):i.addErrorMessage(()=>"Unknown error");break;default:throw Error("not implemented: "+t.kind)}})(e,o,a);let{message:l,args:u}=rj(o,i);throw new iE(rt({message:l,callsite:n,originalMethod:r,showColors:"pretty"===i,callArguments:u}),{clientVersion:s})}function rL(e){return e.replace(/^./,e=>e.toLowerCase())}function rM(e,t,i){return i?ih(i,({needs:e,compute:i},n)=>{let r;return{name:n,needs:e?Object.keys(e).filter(t=>e[t]):[],compute:(r=t?.[n]?.compute)?e=>i({...e,[n]:r(e)}):i}}):{}}var rG=class{constructor(e,t){this.computedFieldsCache=new nz,this.modelExtensionsCache=new nz,this.queryCallbacksCache=new nz,this.clientExtensions=nW(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions()),this.batchCallbacks=nW(()=>{let e=this.previous?.getAllBatchQueryCallbacks()??[],t=this.extension.query?.$__internalBatch;return t?e.concat(t):e}),this.extension=e,this.previous=t}getAllComputedFields(e){return this.computedFieldsCache.getOrCreate(e,()=>{var t,i,n;let r,s,a;return t=this.previous?.getAllComputedFields(e),i=this.extension,r=rL(e),i.result&&(i.result.$allModels||i.result[r])?(n={...t,...rM(i.name,t,i.result.$allModels),...rM(i.name,t,i.result[r])},s=new nz,a=(e,t)=>s.getOrCreate(e,()=>t.has(e)?[e]:(t.add(e),n[e]?n[e].needs.flatMap(e=>a(e,t)):[e])),ih(n,e=>({...e,needs:a(e.name,new Set)}))):t})}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(e){return this.modelExtensionsCache.getOrCreate(e,()=>{let t=rL(e);return this.extension.model&&(this.extension.model[t]||this.extension.model.$allModels)?{...this.previous?.getAllModelExtensions(e),...this.extension.model.$allModels,...this.extension.model[t]}:this.previous?.getAllModelExtensions(e)})}getAllQueryCallbacks(e,t){return this.queryCallbacksCache.getOrCreate(`${e}:${t}`,()=>{let i=this.previous?.getAllQueryCallbacks(e,t)??[],n=[],r=this.extension.query;return r&&(r[e]||r.$allModels||r[t]||r.$allOperations)?(void 0!==r[e]&&(void 0!==r[e][t]&&n.push(r[e][t]),void 0!==r[e].$allOperations&&n.push(r[e].$allOperations)),"$none"!==e&&void 0!==r.$allModels&&(void 0!==r.$allModels[t]&&n.push(r.$allModels[t]),void 0!==r.$allModels.$allOperations&&n.push(r.$allModels.$allOperations)),void 0!==r[t]&&n.push(r[t]),void 0!==r.$allOperations&&n.push(r.$allOperations),i.concat(n)):i})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},rB=class e{constructor(e){this.head=e}static empty(){return new e}static single(t){return new e(new rG(t))}isEmpty(){return void 0===this.head}append(t){return new e(new rG(t,this.head))}getAllComputedFields(e){return this.head?.getAllComputedFields(e)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(e){return this.head?.getAllModelExtensions(e)}getAllQueryCallbacks(e,t){return this.head?.getAllQueryCallbacks(e,t)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}},rQ=class{constructor(e){this.name=e}};function rz(e){return new rQ(e)}var rJ=Symbol(),rW=class{constructor(e){if(e!==rJ)throw Error("Skip instance can not be constructed directly")}ifUndefined(e){return void 0===e?rH:e}},rH=new rW(rJ);function rK(e){return e instanceof rW}var rY={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},rZ="explicitly `undefined` values are not allowed";function rX({modelName:e,action:t,args:i,runtimeDataModel:n,extensions:r=rB.empty(),callsite:s,clientMethod:a,errorFormat:o,clientVersion:l,previewFeatures:u,globalOmit:d}){let c=new r1({runtimeDataModel:n,modelName:e,action:t,rootArgs:i,callsite:s,extensions:r,selectionPath:[],argumentPath:[],originalMethod:a,errorFormat:o,clientVersion:l,previewFeatures:u,globalOmit:d});return{modelName:e,action:rY[t],query:function e({select:t,include:i,...n}={},r){let s,a=n.omit;return delete n.omit,{arguments:function e(t,i){if(t.$type)return{$type:"Raw",value:t};let n={};for(let r in t){let s=t[r],a=i.nestArgument(r);rK(s)||(void 0!==s?n[r]=function t(i,n){if(null===i)return null;if("string"==typeof i||"number"==typeof i||"boolean"==typeof i)return i;if("bigint"==typeof i)return{$type:"BigInt",value:String(i)};if(nY(i)){if(nZ(i))return{$type:"DateTime",value:i.toISOString()};n.throwValidationError({kind:"InvalidArgumentValue",selectionPath:n.getSelectionPath(),argumentPath:n.getArgumentPath(),argument:{name:n.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(i instanceof rQ)return{$type:"Param",value:i.name};if(rS(i))return{$type:"FieldRef",value:{_ref:i.name,_container:i.modelName}};if(Array.isArray(i))return function(e,i){let n=[];for(let r=0;r<e.length;r++){let s=i.nestArgument(String(r)),a=e[r];if(void 0===a||rK(a)){let e=void 0===a?"undefined":"Prisma.skip";i.throwValidationError({kind:"InvalidArgumentValue",selectionPath:s.getSelectionPath(),argumentPath:s.getArgumentPath(),argument:{name:`${i.getArgumentName()}[${r}]`,typeNames:[]},underlyingError:`Can not use \`${e}\` value within array. Use \`null\` or filter out \`${e}\` values`})}n.push(t(a,s))}return n}(i,n);if(ArrayBuffer.isView(i)){let{buffer:e,byteOffset:t,byteLength:n}=i;return{$type:"Bytes",value:Buffer.from(e,t,n).toString("base64")}}if("object"==typeof i&&null!==i&&!0===i.__prismaRawParameters__)return i.values;if(nX(i))return{$type:"Decimal",value:i.toFixed()};if(i instanceof rO){if(i!==rN.instances[i._getName()])throw Error("Invalid ObjectEnumValue");return{$type:"Enum",value:i._getName()}}return"object"==typeof i&&null!==i&&"function"==typeof i.toJSON?i.toJSON():"object"==typeof i?e(i,n):void n.throwValidationError({kind:"InvalidArgumentValue",selectionPath:n.getSelectionPath(),argumentPath:n.getArgumentPath(),argument:{name:n.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(i)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}(s,a):i.isPreviewFeatureOn("strictUndefinedChecks")&&i.throwValidationError({kind:"InvalidArgumentValue",argumentPath:a.getArgumentPath(),selectionPath:i.getSelectionPath(),argument:{name:i.getArgumentName(),typeNames:[]},underlyingError:rZ}))}return n}(n,r),selection:t?(i?r.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:r.getSelectionPath()}):a&&r.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:r.getSelectionPath()}),function(t,i){let n={},r=i.getComputedFields();for(let[s,a]of Object.entries(function(e,t){if(!t)return e;let i={...e};for(let n of Object.values(t))if(e[n.name])for(let e of n.needs)i[e]=!0;return i}(t,r))){if(rK(a))continue;let t=i.nestSelection(s);r0(a,t);let o=i.findField(s);if(!(r?.[s]&&!o)){if(!1===a||void 0===a||rK(a)){n[s]=!1;continue}if(!0===a){o?.kind==="object"?n[s]=e({},t):n[s]=!0;continue}n[s]=e(a,t)}}return n}(t,r)):(s={},r.modelOrType&&!r.isRawAction()&&(s.$composites=!0,s.$scalars=!0),i&&function(t,i,n){for(let[r,s]of Object.entries(i)){if(rK(s))continue;let i=n.nestSelection(r);if(r0(s,i),!1===s||void 0===s){t[r]=!1;continue}let a=n.findField(r);if(a&&"object"!==a.kind&&n.throwValidationError({kind:"IncludeOnScalar",selectionPath:n.getSelectionPath().concat(r),outputType:n.getOutputTypeDescription()}),a){t[r]=e(!0===s?{}:s,i);continue}if(!0===s){t[r]=!0;continue}t[r]=e(s,i)}}(s,i,r),function(e,t,i){let n=i.getComputedFields();for(let[r,s]of Object.entries(function(e,t){if(!t)return e;let i={...e};for(let n of Object.values(t))if(!e[n.name])for(let e of n.needs)delete i[e];return i}({...i.getGlobalOmit(),...t},n))){if(rK(s))continue;r0(s,i.nestSelection(r));let t=i.findField(r);n?.[r]&&!t||(e[r]=!s)}}(s,a,r),s)}}(i,c)}}function r0(e,t){void 0===e&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:rZ})}var r1=class e{constructor(e){this.params=e,this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}throwValidationError(e){rC({errors:[e],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(e=>({name:e.name,typeName:"boolean",isRelation:"object"===e.kind}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(e){return this.params.previewFeatures.includes(e)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(e){return this.modelOrType?.fields.find(t=>t.name===e)}nestSelection(t){let i=this.findField(t),n=i?.kind==="object"?i.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[nJ(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:ii(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function r2(e){if(!e._hasPreviewFlag("metrics"))throw new iE("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var r4=class{constructor(e){this._client=e}prometheus(e){return r2(this._client),this._client._engine.metrics({format:"prometheus",...e})}json(e){return r2(this._client),this._client._engine.metrics({format:"json",...e})}};function r3(e,t){let i=nW(()=>({datamodel:{models:r6(t.models),enums:r6(t.enums),types:r6(t.types)}}));Object.defineProperty(e,"dmmf",{get:()=>i.get()})}function r6(e){return Object.entries(e).map(([e,t])=>({name:e,...t}))}var r7=new WeakMap,r9="$$PrismaTypedSql",r5=class{constructor(e,t){r7.set(this,{sql:e,values:t}),Object.defineProperty(this,r9,{value:r9})}get sql(){return r7.get(this).sql}get values(){return r7.get(this).values}};function r8(e){return(...t)=>new r5(e,t)}function se(e){return null!=e&&e[r9]===r9}var st=h(b()),si=i(16698),sn=i(78474),sr=h(i(73024)),ss=h(i(76760)),sa=class e{constructor(t,i){if(t.length-1!==i.length)throw 0===t.length?TypeError("Expected at least 1 string"):TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=i.reduce((t,i)=>t+(i instanceof e?i.values.length:1),0);this.values=Array(n),this.strings=Array(n+1),this.strings[0]=t[0];let r=0,s=0;for(;r<i.length;){let n=i[r++],a=t[r];if(n instanceof e){this.strings[s]+=n.strings[0];let e=0;for(;e<n.values.length;)this.values[s++]=n.values[e++],this.strings[s]=n.strings[e];this.strings[s]+=a}else this.values[s++]=n,this.strings[s]=a}}get sql(){let e=this.strings.length,t=1,i=this.strings[0];for(;t<e;)i+=`?${this.strings[t++]}`;return i}get statement(){let e=this.strings.length,t=1,i=this.strings[0];for(;t<e;)i+=`:${t}${this.strings[t++]}`;return i}get text(){let e=this.strings.length,t=1,i=this.strings[0];for(;t<e;)i+=`$${t}${this.strings[t++]}`;return i}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function so(e,t=",",i="",n=""){if(0===e.length)throw TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new sa([i,...Array(e.length-1).fill(t),n],e)}function sl(e){return new sa([e],[])}var su=sl("");function sd(e,...t){return new sa(e,t)}function sc(e){return{getKeys:()=>Object.keys(e),getPropertyValue:t=>e[t]}}function sf(e,t){return{getKeys:()=>[e],getPropertyValue:()=>t()}}function sh(e){let t=new nz;return{getKeys:()=>e.getKeys(),getPropertyValue:i=>t.getOrCreate(i,()=>e.getPropertyValue(i)),getPropertyDescriptor:t=>e.getPropertyDescriptor?.(t)}}var sp={enumerable:!0,configurable:!0,writable:!0};function sm(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>sp,has:(e,i)=>t.has(i),set:(e,i,n)=>t.add(i)&&Reflect.set(e,i,n),ownKeys:()=>[...t]}}var sg=Symbol.for("nodejs.util.inspect.custom");function sy(e,t){let i=function(e){let t=new Map;for(let i of e)for(let e of i.getKeys())t.set(e,i);return t}(t),n=new Set,r=new Proxy(e,{get(e,t){if(n.has(t))return e[t];let r=i.get(t);return r?r.getPropertyValue(t):e[t]},has(e,t){if(n.has(t))return!0;let r=i.get(t);return r?r.has?.(t)??!0:Reflect.has(e,t)},ownKeys:e=>[...new Set([...sb(Reflect.ownKeys(e),i),...sb(Array.from(i.keys()),i),...n])],set:(e,t,r)=>i.get(t)?.getPropertyDescriptor?.(t)?.writable!==!1&&(n.add(t),Reflect.set(e,t,r)),getOwnPropertyDescriptor(e,t){let n=Reflect.getOwnPropertyDescriptor(e,t);if(n&&!n.configurable)return n;let r=i.get(t);return r?r.getPropertyDescriptor?{...sp,...r?.getPropertyDescriptor(t)}:sp:n},defineProperty:(e,t,i)=>(n.add(t),Reflect.defineProperty(e,t,i)),getPrototypeOf:()=>Object.prototype});return r[sg]=function(){let e={...this};return delete e[sg],e},r}function sb(e,t){return e.filter(e=>t.get(e)?.has?.(e)??!0)}function sw(e){return{getKeys:()=>e,has:()=>!1,getPropertyValue(){}}}function sv(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function sE({error:e,user_facing_error:t},i,n){let r;return t.error_code?new ib((r=t.message,("postgresql"===n||"postgres"===n||"mysql"===n)&&"P2037"===t.error_code&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r),{code:t.error_code,clientVersion:i,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new iv(e,{clientVersion:i,batchRequestIdx:t.batch_request_idx})}var sx="<unknown>",sP=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,sR=/\((\S*)(?::(\d+))(?::(\d+))\)/,s_=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,sS=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,sA=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,sT=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i,sO=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i,sq=class{getLocation(){return null}},sI=class{constructor(){this._error=Error()}getLocation(){let e=this._error.stack;if(!e)return null;let t=e.split(`
`).reduce(function(e,t){var i,n,r,s=function(e){var t=sP.exec(e);if(!t)return null;var i=t[2]&&0===t[2].indexOf("native"),n=t[2]&&0===t[2].indexOf("eval"),r=sR.exec(t[2]);return n&&null!=r&&(t[2]=r[1],t[3]=r[2],t[4]=r[3]),{file:i?null:t[2],methodName:t[1]||sx,arguments:i?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}(t)||((i=s_.exec(t))?{file:i[2],methodName:i[1]||sx,arguments:[],lineNumber:+i[3],column:i[4]?+i[4]:null}:null)||function(e){var t=sS.exec(e);if(!t)return null;var i=t[3]&&t[3].indexOf(" > eval")>-1,n=sA.exec(t[3]);return i&&null!=n&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||sx,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}(t)||((n=sO.exec(t))?{file:n[2],methodName:n[1]||sx,arguments:[],lineNumber:+n[3],column:n[4]?+n[4]:null}:null)||((r=sT.exec(t))?{file:r[3],methodName:r[1]||sx,arguments:[],lineNumber:+r[4],column:r[5]?+r[5]:null}:null);return s&&e.push(s),e},[]).find(e=>{var t;if(!e.file)return!1;let i=(t=e.file,ir.default.sep===ir.default.posix.sep?t:t.split(ir.default.sep).join(ir.default.posix.sep));return"<anonymous>"!==i&&!i.includes("@prisma")&&!i.includes("/packages/client/src/runtime/")&&!i.endsWith("/runtime/binary.js")&&!i.endsWith("/runtime/library.js")&&!i.endsWith("/runtime/edge.js")&&!i.endsWith("/runtime/edge-esm.js")&&!i.startsWith("internal/")&&!e.methodName.includes("new ")&&!e.methodName.includes("getCallSite")&&!e.methodName.includes("Proxy.")&&e.methodName.split(".").length<4});return t&&t.file?{fileName:t.file,lineNumber:t.lineNumber,columnNumber:t.column}:null}};function sk(e){return"minimal"===e?"function"==typeof $EnabledCallSite&&"minimal"!==e?new $EnabledCallSite:new sq:new sI}var s$={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function sN(e={}){return Object.entries(function(e={}){return"boolean"==typeof e._count?{...e,_count:{_all:e._count}}:e}(e)).reduce((e,[t,i])=>(void 0!==s$[t]?e.select[t]={select:i}:e[t]=i,e),{select:{}})}function sD(e={}){return t=>("boolean"==typeof e._count&&(t._count=t._count._all),t)}function sF(e={}){let{select:t,...i}=e;return"object"==typeof t?sN({...i,_count:t}):sN({...i,_count:{_all:!0}})}function sU(e={}){let t=sN(e);if(Array.isArray(t.by))for(let e of t.by)"string"==typeof e&&(t.select[e]=!0);else"string"==typeof t.by&&(t.select[t.by]=!0);return t}var sV=e=>Array.isArray(e)?e:e.split("."),sj=(e,t)=>sV(t).reduce((e,t)=>e&&e[t],e),sC=(e,t,i)=>sV(t).reduceRight((t,i,n,r)=>Object.assign({},sj(e,r.slice(0,n)),{[i]:t}),i),sL=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],sM=["aggregate","count","groupBy"];function sG(e,t){let i,n,r=e._extensions.getAllModelExtensions(t)??{};return sy({},[(i=rL(t),n=Object.keys(n2).concat("count"),{getKeys:()=>n,getPropertyValue(n){let r=r=>s=>{let a=sk(e._errorFormat);return e._createPrismaPromise(o=>{let l={args:s,dataPath:[],action:n,model:t,clientMethod:`${i}.${n}`,jsModelName:i,transaction:o,callsite:a};return e._request({...l,...r})},{action:n,args:s,model:t})};return sL.includes(n)?function e(t,i,n,r,s,a){let o=t._runtimeDataModel.models[i].fields.reduce((e,t)=>({...e,[t.name]:t}),{});return l=>{let u=sk(t._errorFormat),d=void 0===r||void 0===s?[]:[...s,"select",r],c=void 0===a?l??{}:sC(a,d,l||!0),f=n({dataPath:d,callsite:u})(c),h=t._runtimeDataModel.models[i].fields.filter(e=>"object"===e.kind).map(e=>e.name);return new Proxy(f,{get:(i,r)=>h.includes(r)?e(t,o[r].type,n,r,d,c):i[r],...sm([...h,...Object.getOwnPropertyNames(f)])})}}(e,t,r):sM.includes(n)?"aggregate"===n?e=>r({action:"aggregate",unpacker:sD(e),argsMapper:sN})(e):"count"===n?e=>r({action:"count",unpacker:function(e={}){return"object"==typeof e.select?t=>sD(e)(t)._count:t=>sD(e)(t)._count._all}(e),argsMapper:sF})(e):"groupBy"===n?e=>r({action:"groupBy",unpacker:function(e={}){return t=>("boolean"==typeof e?._count&&t.forEach(e=>{e._count=e._count._all}),t)}(e),argsMapper:sU})(e):void 0:r({})}}),sh(sf("fields",()=>{let i;return new Proxy({},{get(e,n){if(n in e||"symbol"==typeof n)return e[n];let r=i[n];if(r)return new r_(t,n,r.type,r.isList,"enum"===r.kind)},...sm(Object.keys(i=function(e,t){let i={};for(let n of e)i[n[t]]=n;return i}(e._runtimeDataModel.models[t].fields.filter(e=>!e.relationName),"name")))})})),sc(r),sf("name",()=>t),sf("$name",()=>t),sf("$parent",()=>e._appliedParent)])}var sB=Symbol();function sQ(e){let t,i,n,r;let s=[(t=[...new Set(Object.getOwnPropertyNames(Object.getPrototypeOf(e._originalClient)))],{getKeys:()=>t,getPropertyValue:t=>e[t]}),(n=(i=Object.keys(e._runtimeDataModel.models)).map(rL),r=[...new Set(i.concat(n))],sh({getKeys:()=>r,getPropertyValue(t){let i=t.replace(/^./,e=>e.toUpperCase());return void 0!==e._runtimeDataModel.models[i]?sG(e,i):void 0!==e._runtimeDataModel.models[t]?sG(e,t):void 0},getPropertyDescriptor(e){if(!n.includes(e))return{enumerable:!1}}})),sf(sB,()=>e),sf("$parent",()=>e._appliedParent)],a=e._extensions.getAllClientExtensions();return a&&s.push(sc(a)),sy(e,s)}function sz(e){if("function"==typeof e)return e(this);if(e.client?.__AccelerateEngine){let t=e.client.__AccelerateEngine;this._originalClient._engine=new t(this._originalClient._accelerateEngineConfig)}return sQ(Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}}))}function sJ({visitor:e,result:t,args:i,runtimeDataModel:n,modelName:r}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=sJ({result:t[s],args:i,modelName:r,runtimeDataModel:n,visitor:e});return t}let s=e(t,r,i)??t;return i.include&&sW({includeOrSelect:i.include,result:s,parentModelName:r,runtimeDataModel:n,visitor:e}),i.select&&sW({includeOrSelect:i.select,result:s,parentModelName:r,runtimeDataModel:n,visitor:e}),s}function sW({includeOrSelect:e,result:t,parentModelName:i,runtimeDataModel:n,visitor:r}){for(let[s,a]of Object.entries(e)){if(!a||null==t[s]||rK(a))continue;let e=n.models[i].fields.find(e=>e.name===s);if(!e||"object"!==e.kind||!e.relationName)continue;let o="object"==typeof a?a:{};t[s]=sJ({visitor:r,result:t[s],args:o,modelName:e.type,runtimeDataModel:n})}}var sH=["$connect","$disconnect","$on","$transaction","$use","$extends"];function sK(e){if("object"!=typeof e||null==e||e instanceof rO||rS(e))return e;if(nX(e))return new nB(e.toFixed());if(nY(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,i;for(i=Array(t);t--;)i[t]=sK(e[t]);return i}if("object"==typeof e){let t={};for(let i in e)"__proto__"===i?Object.defineProperty(t,i,{value:sK(e[i]),configurable:!0,enumerable:!0,writable:!0}):t[i]=sK(e[i]);return t}ii(e,"Unknown value")}var sY=e=>e;function sZ(e=sY,t=sY){return i=>e(t(i))}var sX=ex("prisma:client"),s0={Vercel:"vercel","Netlify CI":"netlify"},s1=()=>globalThis.process?.release?.name==="node",s2=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,s4=()=>!!globalThis.Deno,s3=()=>"object"==typeof globalThis.Netlify,s6=()=>"object"==typeof globalThis.EdgeRuntime,s7=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers",s9={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function s5(){let e=[[s3,"netlify"],[s6,"edge-light"],[s7,"workerd"],[s4,"deno"],[s2,"bun"],[s1,"node"]].flatMap(e=>e[0]()?[e[1]]:[]).at(0)??"";return{id:e,prettyName:s9[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var s8=h(i(73024)),ae=h(i(76760));function at(e){let{runtimeBinaryTarget:t}=e;return`Add "${t}" to \`binaryTargets\` in the "schema.prisma" file and run \`prisma generate\` after saving it:

${function(e){let{generator:t,generatorBinaryTargets:i,runtimeBinaryTarget:n}=e,r=[...i,{fromEnvVar:null,value:n}];return String(new t2({...t,binaryTargets:r}))}(e)}`}function ai(e){let{runtimeBinaryTarget:t}=e;return`Prisma Client could not locate the Query Engine for runtime "${t}".`}function an(e){let{searchedLocations:t}=e;return`The following locations have been searched:
${[...new Set(t)].map(e=>`  ${e}`).join(`
`)}`}function ar(e){return`We would appreciate if you could take the time to share some information with us.
Please help us by answering a few questions: https://pris.ly/${e}`}function as(e){let{errorStack:t}=e;return t?.match(/\/\.next|\/next@|\/next\//)?`

We detected that you are using Next.js, learn how to fix this: https://pris.ly/d/engine-not-found-nextjs.`:""}var aa=ex("prisma:client:engines:resolveEnginePath"),ao=()=>RegExp("runtime[\\\\/]library\\.m?js$");async function al(e,t){let i={binary:process.env.PRISMA_QUERY_ENGINE_BINARY,library:process.env.PRISMA_QUERY_ENGINE_LIBRARY}[e]??t.prismaPath;if(void 0!==i)return i;let{enginePath:n,searchedLocations:r}=await au(e,t);if(aa("enginePath",n),void 0!==n&&"binary"===e&&function(e){if("win32"===process.platform)return;let t=tY.default.statSync(e),i=73|t.mode;if(t.mode===i){tZ(`Execution permissions of ${e} are fine`);return}let n=i.toString(8).slice(-3);tZ(`Have to call chmodPlusX on ${e}`),tY.default.chmodSync(e,n)}(n),void 0!==n)return t.prismaPath=n;let s=await te(),a=t.generator?.binaryTargets??[],o=a.some(e=>e.native),l=!a.some(e=>e.value===s),u=null===__filename.match(ao()),d={searchedLocations:r,generatorBinaryTargets:a,generator:t.generator,runtimeBinaryTarget:s,queryEngineName:ad(e,s),expectedLocation:ae.default.relative(process.cwd(),t.dirname),errorStack:Error().stack};throw new iy(o&&l?function(e){let{runtimeBinaryTarget:t,generatorBinaryTargets:i}=e,n=i.find(e=>e.native);return`${ai(e)}

This happened because Prisma Client was generated for "${n?.value??"unknown"}", but the actual deployment required "${t}".
${at(e)}

${an(e)}`}(d):l?function(e){let{runtimeBinaryTarget:t}=e;return`${ai(e)}

This happened because \`binaryTargets\` have been pinned, but the actual deployment also required "${t}".
${at(e)}

${an(e)}`}(d):u?function(e){let{queryEngineName:t}=e;return`${ai(e)}${as(e)}

This is likely caused by a bundler that has not copied "${t}" next to the resulting bundle.
Ensure that "${t}" has been copied next to the bundle or in "${e.expectedLocation}".

${ar("engine-not-found-bundler-investigation")}

${an(e)}`}(d):function(e){let{queryEngineName:t}=e;return`${ai(e)}${as(e)}

This is likely caused by tooling that has not copied "${t}" to the deployment folder.
Ensure that you ran \`prisma generate\` and that "${t}" has been copied to "${e.expectedLocation}".

${ar("engine-not-found-tooling-investigation")}

${an(e)}`}(d),t.clientVersion)}async function au(e,t){let i=await te(),n=[],r=[t.dirname,ae.default.resolve(__dirname,".."),t.generator?.output?.value??__dirname,ae.default.resolve(__dirname,"../../../.prisma/client"),"/tmp/prisma-engines",t.cwd];for(let t of(__filename.includes("resolveEnginePath")&&r.push(tK.default.join(__dirname,"../")),r)){let r=ad(e,i),s=ae.default.join(t,r);if(n.push(t),s8.default.existsSync(s))return{enginePath:s,searchedLocations:n}}return{enginePath:void 0,searchedLocations:n}}function ad(e,t){return"library"===e?t.includes("windows")?`query_engine-${t}.dll.node`:t.includes("darwin")?`${eR}-${t}.dylib.node`:`${eR}-${t}.so.node`:`query-engine-${t}${"windows"===t?".exe":""}`}var ac=h(P()),af=h(S());function ah(e){return"DriverAdapterError"===e.name&&"object"==typeof e.cause}function ap(e){return{ok:!0,value:e,map:t=>ap(t(e)),flatMap:t=>t(e)}}function am(e){return{ok:!1,error:e,map:()=>am(e),flatMap:()=>am(e)}}var ag=ex("driver-adapter-utils"),ay=class{consumeError(e){return this.registeredErrors[e]}registerNewError(e){let t=0;for(;void 0!==this.registeredErrors[t];)t++;return this.registeredErrors[t]={error:e},t}constructor(){this.registeredErrors=[]}},ab=(e,t=new ay)=>{var i;let n={adapterName:e.adapterName,errorRegistry:t,queryRaw:av(t,e.queryRaw.bind(e)),executeRaw:av(t,e.executeRaw.bind(e)),executeScript:av(t,e.executeScript.bind(e)),dispose:av(t,e.dispose.bind(e)),provider:e.provider,startTransaction:async(...i)=>(await av(t,e.startTransaction.bind(e))(...i)).map(e=>aw(t,e))};return e.getConnectionInfo&&(n.getConnectionInfo=(i=e.getConnectionInfo.bind(e),(...e)=>{try{return ap(i(...e))}catch(e){if(ag("[error@wrapSync]",e),ah(e))return am(e.cause);return am({kind:"GenericJs",id:t.registerNewError(e)})}})),n},aw=(e,t)=>({adapterName:t.adapterName,provider:t.provider,options:t.options,queryRaw:av(e,t.queryRaw.bind(t)),executeRaw:av(e,t.executeRaw.bind(t)),commit:av(e,t.commit.bind(t)),rollback:av(e,t.rollback.bind(t))});function av(e,t){return async(...i)=>{try{return ap(await t(...i))}catch(t){if(ag("[error@wrapAsync]",t),ah(t))return am(t.cause);return am({kind:"GenericJs",id:e.registerNewError(t)})}}}function aE({inlineDatasources:e,overrideDatasources:t,env:i,clientVersion:n}){let r,s=Object.keys(e)[0],a=e[s]?.url,o=t[s]?.url;if(void 0===s?r=void 0:o?r=o:a?.value?r=a.value:a?.fromEnvVar&&(r=i[a.fromEnvVar]),a?.fromEnvVar!==void 0&&void 0===r)throw new iy(`error: Environment variable not found: ${a.fromEnvVar}.`,n);if(void 0===r)throw new iy("error: Missing URL environment variable, value, or override.",n);return r}var ax=class extends Error{constructor(e,t){super(e),this.clientVersion=t.clientVersion,this.cause=t.cause}get[Symbol.toStringTag](){return this.name}},aP=class extends ax{constructor(e,t){super(e,t),this.isRetryable=t.isRetryable??!0}};function aR(e,t){return{...e,isRetryable:t}}var a_=class extends aP{constructor(e){super("This request must be retried",aR(e,!0)),this.name="ForcedRetryError",this.code="P5001"}};ip(a_,"ForcedRetryError");var aS=class extends aP{constructor(e,t){super(e,aR(t,!1)),this.name="InvalidDatasourceError",this.code="P6001"}};ip(aS,"InvalidDatasourceError");var aA=class extends aP{constructor(e,t){super(e,aR(t,!1)),this.name="NotImplementedYetError",this.code="P5004"}};ip(aA,"NotImplementedYetError");var aT=class extends aP{constructor(e,t){super(e,t),this.response=t.response;let i=this.response.headers.get("prisma-request-id");if(i){let e=`(The request id was: ${i})`;this.message=this.message+" "+e}}},aO=class extends aT{constructor(e){super("Schema needs to be uploaded",aR(e,!0)),this.name="SchemaMissingError",this.code="P5005"}};ip(aO,"SchemaMissingError");var aq="This request could not be understood by the server",aI=class extends aT{constructor(e,t,i){super(t||aq,aR(e,!1)),this.name="BadRequestError",this.code="P5000",i&&(this.code=i)}};ip(aI,"BadRequestError");var ak=class extends aT{constructor(e,t){super("Engine not started: healthcheck timeout",aR(e,!0)),this.name="HealthcheckTimeoutError",this.code="P5013",this.logs=t}};ip(ak,"HealthcheckTimeoutError");var a$=class extends aT{constructor(e,t,i){super(t,aR(e,!0)),this.name="EngineStartupError",this.code="P5014",this.logs=i}};ip(a$,"EngineStartupError");var aN=class extends aT{constructor(e){super("Engine version is not supported",aR(e,!1)),this.name="EngineVersionNotSupportedError",this.code="P5012"}};ip(aN,"EngineVersionNotSupportedError");var aD="Request timed out",aF=class extends aT{constructor(e,t=aD){super(t,aR(e,!1)),this.name="GatewayTimeoutError",this.code="P5009"}};ip(aF,"GatewayTimeoutError");var aU=class extends aT{constructor(e,t="Interactive transaction error"){super(t,aR(e,!1)),this.name="InteractiveTransactionError",this.code="P5015"}};ip(aU,"InteractiveTransactionError");var aV=class extends aT{constructor(e,t="Request parameters are invalid"){super(t,aR(e,!1)),this.name="InvalidRequestError",this.code="P5011"}};ip(aV,"InvalidRequestError");var aj="Requested resource does not exist",aC=class extends aT{constructor(e,t=aj){super(t,aR(e,!1)),this.name="NotFoundError",this.code="P5003"}};ip(aC,"NotFoundError");var aL="Unknown server error",aM=class extends aT{constructor(e,t,i){super(t||aL,aR(e,!0)),this.name="ServerError",this.code="P5006",this.logs=i}};ip(aM,"ServerError");var aG="Unauthorized, check your connection string",aB=class extends aT{constructor(e,t=aG){super(t,aR(e,!1)),this.name="UnauthorizedError",this.code="P5007"}};ip(aB,"UnauthorizedError");var aQ="Usage exceeded, retry again later",az=class extends aT{constructor(e,t=aQ){super(t,aR(e,!0)),this.name="UsageExceededError",this.code="P5008"}};async function aJ(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let e=JSON.parse(t);if("string"==typeof e){if("InternalDataProxyError"===e)return{type:"DataProxyError",body:e};return{type:"UnknownTextError",body:e}}if("object"==typeof e&&null!==e){if("is_panic"in e&&"message"in e&&"error_code"in e)return{type:"QueryEngineError",body:e};if("EngineNotStarted"in e||"InteractiveTransactionMisrouted"in e||"InvalidRequestError"in e){let t=Object.values(e)[0].reason;return"string"!=typeof t||["SchemaMissing","EngineVersionNotSupported"].includes(t)?{type:"DataProxyError",body:e}:{type:"UnknownJsonError",body:e}}}return{type:"UnknownJsonError",body:e}}catch{return""===t?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function aW(e,t){if(e.ok)return;let i={clientVersion:t,response:e},n=await aJ(e);if("QueryEngineError"===n.type)throw new ib(n.body.message,{code:n.body.error_code,clientVersion:t});if("DataProxyError"===n.type){if("InternalDataProxyError"===n.body)throw new aM(i,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if("SchemaMissing"===n.body.EngineNotStarted.reason)return new aO(i);if("EngineVersionNotSupported"===n.body.EngineNotStarted.reason)throw new aN(i);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:e,logs:t}=n.body.EngineNotStarted.reason.EngineStartupError;throw new a$(i,e,t)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:e,error_code:i}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new iy(e,t,i)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:e}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new ak(i,e)}}if("InteractiveTransactionMisrouted"in n.body)throw new aU(i,{IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"}[n.body.InteractiveTransactionMisrouted.reason]);if("InvalidRequestError"in n.body)throw new aV(i,n.body.InvalidRequestError.reason)}if(401===e.status||403===e.status)throw new aB(i,aH(aG,n));if(404===e.status)return new aC(i,aH(aj,n));if(429===e.status)throw new az(i,aH(aQ,n));if(504===e.status)throw new aF(i,aH(aD,n));if(e.status>=500)throw new aM(i,aH(aL,n));if(e.status>=400)throw new aI(i,aH(aq,n))}function aH(e,t){return"EmptyError"===t.type?e:`${e}: ${JSON.stringify(t)}`}ip(az,"UsageExceededError");var aK="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function aY(e){return new Date(1e3*e[0]+e[1]/1e6)}var aZ={"@prisma/engines-version":"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e"},aX=class extends aP{constructor(e,t){super(`Cannot fetch data from service:
${e}`,aR(t,!0)),this.name="RequestError",this.code="P5010"}};async function a0(e,t,i=e=>e){let{clientVersion:n,...r}=t,s=i(fetch);try{return await s(e,r)}catch(e){throw new aX(e.message??"Unknown error",{clientVersion:n,cause:e})}}ip(aX,"RequestError");var a1=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,a2=ex("prisma:client:dataproxyEngine");async function a4(e,t){let i=aZ["@prisma/engines-version"],n=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&"0.0.0"!==n&&"in-memory"!==n)return n;let[r,s]=n?.split("-")??[];if(void 0===s&&a1.test(r))return r;if(void 0!==s||"0.0.0"===n||"in-memory"===n){var a;let e,[t]=i.split("-")??[],[r,s,o]=t.split("."),l=(a=`<=${r}.${s}.${o}`,encodeURI(`https://unpkg.com/prisma@${a}/package.json`)),u=await a0(l,{clientVersion:n});if(!u.ok)throw Error(`Failed to fetch stable Prisma version, unpkg.com status ${u.status} ${u.statusText}, response body: ${await u.text()||"<empty body>"}`);let d=await u.text();a2("length of body fetched from unpkg.com",d.length);try{e=JSON.parse(d)}catch(e){throw console.error("JSON.parse error: body fetched from unpkg.com: ",d),e}return e.version}throw new aA("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function a3(e,t){let i=await a4(e,t);return a2("version",i),i}var a6,a7=ex("prisma:client:dataproxyEngine"),a9=class{constructor({apiKey:e,tracingHelper:t,logLevel:i,logQueries:n,engineHash:r}){this.apiKey=e,this.tracingHelper=t,this.logLevel=i,this.logQueries=n,this.engineHash=r}build({traceparent:e,interactiveTransaction:t}={}){let i={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(i.traceparent=e??this.tracingHelper.getTraceParent()),t&&(i["X-transaction-id"]=t.id);let n=this.buildCaptureSettings();return n.length>0&&(i["X-capture-telemetry"]=n.join(", ")),i}buildCaptureSettings(){let e=[];return this.tracingHelper.isEnabled()&&e.push("tracing"),this.logLevel&&e.push(this.logLevel),this.logQueries&&e.push("query"),e}},a5=class{constructor(e){this.name="DataProxyEngine",function(e){if(e.generator?.previewFeatures.some(e=>e.toLowerCase().includes("metrics")))throw new iy("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}(e),this.config=e,this.env={...e.env,..."u">typeof process?process.env:{}},this.inlineSchema=function(e){let t=new TextEncoder().encode(e),i="",n=t.byteLength,r=n%3,s=n-r,a,o,l,u,d;for(let e=0;e<s;e+=3)a=(0xfc0000&(d=t[e]<<16|t[e+1]<<8|t[e+2]))>>18,o=(258048&d)>>12,l=(4032&d)>>6,u=63&d,i+=aK[a]+aK[o]+aK[l]+aK[u];return 1==r?(a=(252&(d=t[s]))>>2,o=(3&d)<<4,i+=aK[a]+aK[o]+"=="):2==r&&(a=(64512&(d=t[s]<<8|t[s+1]))>>10,o=(1008&d)>>4,l=(15&d)<<2,i+=aK[a]+aK[o]+aK[l]+"="),i}(e.inlineSchema),this.inlineDatasources=e.inlineDatasources,this.inlineSchemaHash=e.inlineSchemaHash,this.clientVersion=e.clientVersion,this.engineHash=e.engineVersion,this.logEmitter=e.logEmitter,this.tracingHelper=e.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){void 0!==this.startPromise&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:e,url:t}=this.getURLAndAPIKey();this.host=t.host,this.headerBuilder=new a9({apiKey:e,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.protocol=!function(e){if(!t0(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")}(t)?"https":"http",this.remoteClientVersion=await a3(this.host,this.config),a7("host",this.host),a7("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(e){e?.logs?.length&&e.logs.forEach(e=>{switch(e.level){case"debug":case"trace":a7(e);break;case"error":case"warn":case"info":this.logEmitter.emit(e.level,{timestamp:aY(e.timestamp),message:e.attributes.message??"",target:e.target});break;case"query":this.logEmitter.emit("query",{query:e.attributes.query??"",timestamp:aY(e.timestamp),duration:e.attributes.duration_ms??0,params:e.attributes.params??"",target:e.target});break;default:e.level}}),e?.traces?.length&&this.tracingHelper.dispatchEngineSpans(e.traces)}onBeforeExit(){throw Error('"beforeExit" hook is not applicable to the remote query engine')}async url(e){return await this.start(),`${this.protocol}://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${e}`}async uploadSchema(){return this.tracingHelper.runInChildSpan({name:"schemaUpload",internal:!0},async()=>{let e=await a0(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});e.ok||a7("schema response status",e.status);let t=await aW(e,this.clientVersion);if(t)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${t.message}`,timestamp:new Date,target:""}),t;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(e,{traceparent:t,interactiveTransaction:i,customDataProxyFetch:n}){return this.requestInternal({body:e,traceparent:t,interactiveTransaction:i,customDataProxyFetch:n})}async requestBatch(e,{traceparent:t,transaction:i,customDataProxyFetch:n}){let r=i?.kind==="itx"?i.options:void 0,s=sv(e,i);return(await this.requestInternal({body:s,customDataProxyFetch:n,interactiveTransaction:r,traceparent:t})).map(e=>(e.extensions&&this.propagateResponseExtensions(e.extensions),"errors"in e?this.convertProtocolErrorsToClientError(e.errors):e))}requestInternal({body:e,traceparent:t,customDataProxyFetch:i,interactiveTransaction:n}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:r})=>{let s=n?`${n.payload.endpoint}/graphql`:await this.url("graphql");r(s);let a=await a0(s,{method:"POST",headers:this.headerBuilder.build({traceparent:t,interactiveTransaction:n}),body:JSON.stringify(e),clientVersion:this.clientVersion},i);a.ok||a7("graphql response status",a.status),await this.handleError(await aW(a,this.clientVersion));let o=await a.json();if(o.extensions&&this.propagateResponseExtensions(o.extensions),"errors"in o)throw this.convertProtocolErrorsToClientError(o.errors);return"batchResult"in o?o.batchResult:o}})}async transaction(e,t,i){return this.withRetry({actionGerund:`${{start:"starting",commit:"committing",rollback:"rolling back"}[e]} transaction`,callback:async({logHttpCall:n})=>{if("start"===e){let e=JSON.stringify({max_wait:i.maxWait,timeout:i.timeout,isolation_level:i.isolationLevel}),r=await this.url("transaction/start");n(r);let s=await a0(r,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),body:e,clientVersion:this.clientVersion});await this.handleError(await aW(s,this.clientVersion));let a=await s.json(),{extensions:o}=a;return o&&this.propagateResponseExtensions(o),{id:a.id,payload:{endpoint:a["data-proxy"].endpoint}}}{let r=`${i.payload.endpoint}/${e}`;n(r);let s=await a0(r,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),clientVersion:this.clientVersion});await this.handleError(await aW(s,this.clientVersion));let{extensions:a}=await s.json();a&&this.propagateResponseExtensions(a);return}}})}getURLAndAPIKey(){let e={clientVersion:this.clientVersion},t=Object.keys(this.inlineDatasources)[0],i=aE({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),n;try{n=new URL(i)}catch{throw new aS(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\``,e)}let{protocol:r,searchParams:s}=n;if("prisma:"!==r&&r!==tX)throw new aS(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,e);let a=s.get("api_key");if(null===a||a.length<1)throw new aS(`Error validating datasource \`${t}\`: the URL must contain a valid API key`,e);return{apiKey:a,url:n}}metrics(){throw new aA("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(e){for(let t=0;;t++){let i=e=>{this.logEmitter.emit("info",{message:`Calling ${e} (n=${t})`,timestamp:new Date,target:""})};try{return await e.callback({logHttpCall:i})}catch(n){if(!(n instanceof aP)||!n.isRetryable)throw n;if(t>=3)throw n instanceof a_?n.cause:n;this.logEmitter.emit("warn",{message:`Attempt ${t+1}/3 failed for ${e.actionGerund}: ${n.message??"(unknown)"}`,timestamp:new Date,target:""});let i=await function(e){let t=50*Math.pow(2,e),i=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+i;return new Promise(e=>setTimeout(()=>e(n),n))}(t);this.logEmitter.emit("warn",{message:`Retrying after ${i}ms`,timestamp:new Date,target:""})}}}async handleError(e){if(e instanceof aO)throw await this.uploadSchema(),new a_({clientVersion:this.clientVersion,cause:e});if(e)throw e}convertProtocolErrorsToClientError(e){return 1===e.length?sE(e[0],this.config.clientVersion,this.config.activeProvider):new iv(JSON.stringify(e),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw Error("Method not implemented.")}},a8=h(i(48161)),oe=h(i(76760)),ot=Symbol("PrismaLibraryEngineCache"),oi={async loadLibrary(e){let t=await tt(),i=await al("library",e);try{return e.tracingHelper.runInChildSpan({name:"loadLibrary",internal:!0},()=>(function(e){let t;let i=(void 0===(t=globalThis)[ot]&&(t[ot]={}),t[ot]);if(void 0!==i[e])return i[e];let n=oe.default.toNamespacedPath(e),r={exports:{}},s=0;return"win32"!==process.platform&&(s=a8.default.constants.dlopen.RTLD_LAZY|a8.default.constants.dlopen.RTLD_DEEPBIND),process.dlopen(r,n,s),i[e]=r.exports,r.exports})(i))}catch(c){var n,r;let s,a,o,l,u,d;throw new iy((s=(n={e:c,platformInfo:t,id:i}).e,a=e=>`Prisma cannot find the required \`${e}\` system library in your system`,o=s.message.includes("cannot open shared object file"),l=`Please refer to the documentation about Prisma's system requirements: ${tJ(r="https://pris.ly/d/system-requirements",r,{fallback:W})}`,u=`Unable to require(\`${z(n.id)}\`).`,d=eW({message:s.message,code:s.code}).with({code:"ENOENT"},()=>"File does not exist.").when(({message:e})=>o&&e.includes("libz"),()=>`${a("libz")}. Please install it and try again.`).when(({message:e})=>o&&e.includes("libgcc_s"),()=>`${a("libgcc_s")}. Please install it and try again.`).when(({message:e})=>o&&e.includes("libssl"),()=>{let e=n.platformInfo.libssl?`openssl-${n.platformInfo.libssl}`:"openssl";return`${a("libssl")}. Please install ${e} and try again.`}).when(({message:e})=>e.includes("GLIBC"),()=>`Prisma has detected an incompatible version of the \`glibc\` C standard library installed in your system. This probably means your system may be too old to run Prisma. ${l}`).when(({message:e})=>"linux"===n.platformInfo.platform&&e.includes("symbol not found"),()=>`The Prisma engines are not compatible with your system ${n.platformInfo.originalDistro} on (${n.platformInfo.archFromUname}) which uses the \`${n.platformInfo.binaryTarget}\` binaryTarget by default. ${l}`).otherwise(()=>`The Prisma engines do not seem to be compatible with your system. ${l}`),`${u}
${d}

Details: ${s.message}`),e.clientVersion)}}},on={async loadLibrary(e){let{clientVersion:t,adapter:i,engineWasm:n}=e;if(void 0===i)throw new iy(`The \`adapter\` option for \`PrismaClient\` is required in this context (${s5().prettyName})`,t);if(void 0===n)throw new iy("WASM engine was unexpectedly `undefined`",t);return void 0===a6&&(a6=(async()=>{let e=await n.getRuntime(),i=await n.getQueryEngineWasmModule();if(null==i)throw new iy("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let r=new WebAssembly.Instance(i,{"./query_engine_bg.js":e}),s=r.exports.__wbindgen_start;return e.__wbg_set_wasm(r.exports),s(),e.QueryEngine})()),{debugPanic:()=>Promise.reject("{}"),dmmf:()=>Promise.resolve("{}"),version:()=>({commit:"unknown",version:"unknown"}),QueryEngine:await a6}}},or=ex("prisma:client:libraryEngine"),os=["darwin","darwin-arm64","debian-openssl-1.0.x","debian-openssl-1.1.x","debian-openssl-3.0.x","rhel-openssl-1.0.x","rhel-openssl-1.1.x","rhel-openssl-3.0.x","linux-arm64-openssl-1.1.x","linux-arm64-openssl-1.0.x","linux-arm64-openssl-3.0.x","linux-arm-openssl-1.1.x","linux-arm-openssl-1.0.x","linux-arm-openssl-3.0.x","linux-musl","linux-musl-openssl-3.0.x","linux-musl-arm64-openssl-1.1.x","linux-musl-arm64-openssl-3.0.x","linux-nixos","linux-static-x64","linux-static-arm64","windows","freebsd11","freebsd12","freebsd13","freebsd14","freebsd15","openbsd","netbsd","arm","native"],oa=1n,oo=class{constructor(e,t){this.name="LibraryEngine",this.libraryLoader=t??oi,void 0!==e.engineWasm&&(this.libraryLoader=t??on),this.config=e,this.libraryStarted=!1,this.logQueries=e.logQueries??!1,this.logLevel=e.logLevel??"error",this.logEmitter=e.logEmitter,this.datamodel=e.inlineSchema,this.tracingHelper=e.tracingHelper,e.enableDebugLogs&&(this.logLevel="debug");let i=Object.keys(e.overrideDatasources)[0],n=e.overrideDatasources[i]?.url;void 0!==i&&void 0!==n&&(this.datasourceOverrides={[i]:n}),this.libraryInstantiationPromise=this.instantiateLibrary()}wrapEngine(e){return{applyPendingMigrations:e.applyPendingMigrations?.bind(e),commitTransaction:this.withRequestId(e.commitTransaction.bind(e)),connect:this.withRequestId(e.connect.bind(e)),disconnect:this.withRequestId(e.disconnect.bind(e)),metrics:e.metrics?.bind(e),query:this.withRequestId(e.query.bind(e)),rollbackTransaction:this.withRequestId(e.rollbackTransaction.bind(e)),sdlSchema:e.sdlSchema?.bind(e),startTransaction:this.withRequestId(e.startTransaction.bind(e)),trace:e.trace.bind(e)}}withRequestId(e){return async(...t)=>{let i;let n=(i=oa++,oa>0xffffffffffffffffn&&(oa=1n),i).toString();try{return await e(...t,n)}finally{if(this.tracingHelper.isEnabled()){let e=await this.engine?.trace(n);if(e){let t=JSON.parse(e);this.tracingHelper.dispatchEngineSpans(t.spans)}}}}}async applyPendingMigrations(){throw Error("Cannot call this method from this type of engine instance")}async transaction(e,t,i){await this.start();let n=await this.adapterPromise,r=JSON.stringify(t),s;if("start"===e){let e=JSON.stringify({max_wait:i.maxWait,timeout:i.timeout,isolation_level:i.isolationLevel});s=await this.engine?.startTransaction(e,r)}else"commit"===e?s=await this.engine?.commitTransaction(i.id,r):"rollback"===e&&(s=await this.engine?.rollbackTransaction(i.id,r));let a=this.parseEngineResponse(s);if("object"==typeof a&&null!==a&&void 0!==a.error_code){let e=this.getExternalAdapterError(a,n?.errorRegistry);throw e?e.error:new ib(a.message,{code:a.error_code,clientVersion:this.config.clientVersion,meta:a.meta})}if("string"==typeof a.message)throw new iv(a.message,{clientVersion:this.config.clientVersion});return a}async instantiateLibrary(){if(or("internalSetup"),this.libraryInstantiationPromise)return this.libraryInstantiationPromise;(function(){let e=process.env.PRISMA_QUERY_ENGINE_LIBRARY;if(!(e&&eP.default.existsSync(e))&&"ia32"===process.arch)throw Error('The default query engine type (Node-API, "library") is currently not supported for 32bit Node. Please set `engineType = "binary"` in the "generator" block of your "schema.prisma" file (or use the environment variables "PRISMA_CLIENT_ENGINE_TYPE=binary" and/or "PRISMA_CLI_QUERY_ENGINE_TYPE=binary".)')})(),this.binaryTarget=await this.getCurrentBinaryTarget(),await this.tracingHelper.runInChildSpan("load_engine",()=>this.loadEngine()),this.version()}async getCurrentBinaryTarget(){{if(this.binaryTarget)return this.binaryTarget;let e=await this.tracingHelper.runInChildSpan("detect_platform",()=>te());if(!os.includes(e))throw new iy(`Unknown ${X("PRISMA_QUERY_ENGINE_LIBRARY")} ${X(Q(e))}. Possible binaryTargets: ${ee(os.join(", "))} or a path to the query engine library.
You may have to run ${ee("prisma generate")} for your changes to take effect.`,this.config.clientVersion);return e}}parseEngineResponse(e){if(!e)throw new iv("Response from the Engine was empty",{clientVersion:this.config.clientVersion});try{return JSON.parse(e)}catch{throw new iv("Unable to JSON.parse response from engine",{clientVersion:this.config.clientVersion})}}async loadEngine(){if(!this.engine){this.QueryEngineConstructor||(this.library=await this.libraryLoader.loadLibrary(this.config),this.QueryEngineConstructor=this.library.QueryEngine);try{let e=new WeakRef(this);this.adapterPromise||(this.adapterPromise=this.config.adapter?.connect()?.then(ab));let t=await this.adapterPromise;t&&or("Using driver adapter: %O",t),this.engine=this.wrapEngine(new this.QueryEngineConstructor({datamodel:this.datamodel,env:process.env,logQueries:this.config.logQueries??!1,ignoreEnvVarErrors:!0,datasourceOverrides:this.datasourceOverrides??{},logLevel:this.logLevel,configDir:this.config.cwd,engineProtocol:"json",enableTracing:this.tracingHelper.isEnabled()},t=>{e.deref()?.logger(t)},t))}catch(t){let e=this.parseInitError(t.message);throw"string"==typeof e?t:new iy(e.message,this.config.clientVersion,e.error_code)}}}logger(e){let t=this.parseEngineResponse(e);t&&(t.level=t?.level.toLowerCase()??"unknown","query"===t.item_type&&"query"in t?this.logEmitter.emit("query",{timestamp:new Date,query:t.query,params:t.params,duration:Number(t.duration_ms),target:t.module_path}):"level"in t&&"error"===t.level&&"PANIC"===t.message?this.loggerRustPanic=new iw(ol(this,`${t.message}: ${t.reason} in ${t.file}:${t.line}:${t.column}`),this.config.clientVersion):this.logEmitter.emit(t.level,{timestamp:new Date,message:t.message,target:t.module_path}))}parseInitError(e){try{return JSON.parse(e)}catch{}return e}parseRequestError(e){try{return JSON.parse(e)}catch{}return e}onBeforeExit(){throw Error('"beforeExit" hook is not applicable to the library engine since Prisma 5.0.0, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){if(await this.libraryInstantiationPromise,await this.libraryStoppingPromise,this.libraryStartingPromise)return or(`library already starting, this.libraryStarted: ${this.libraryStarted}`),this.libraryStartingPromise;if(this.libraryStarted)return;let e=async()=>{or("library starting");try{let e={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.connect(JSON.stringify(e)),this.libraryStarted=!0,or("library started")}catch(t){let e=this.parseInitError(t.message);throw"string"==typeof e?t:new iy(e.message,this.config.clientVersion,e.error_code)}finally{this.libraryStartingPromise=void 0}};return this.libraryStartingPromise=this.tracingHelper.runInChildSpan("connect",e),this.libraryStartingPromise}async stop(){if(await this.libraryInstantiationPromise,await this.libraryStartingPromise,await this.executingQueryPromise,this.libraryStoppingPromise)return or("library is already stopping"),this.libraryStoppingPromise;if(!this.libraryStarted)return;let e=async()=>{await new Promise(e=>setTimeout(e,5)),or("library stopping");let e={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.disconnect(JSON.stringify(e)),this.libraryStarted=!1,this.libraryStoppingPromise=void 0,await (await this.adapterPromise)?.dispose(),this.adapterPromise=void 0,or("library stopped")};return this.libraryStoppingPromise=this.tracingHelper.runInChildSpan("disconnect",e),this.libraryStoppingPromise}version(){return this.versionInfo=this.library?.version(),this.versionInfo?.version??"unknown"}debugPanic(e){return this.library?.debugPanic(e)}async request(e,{traceparent:t,interactiveTransaction:i}){or(`sending request, this.libraryStarted: ${this.libraryStarted}`);let n=JSON.stringify({traceparent:t}),r=JSON.stringify(e);try{await this.start();let e=await this.adapterPromise;this.executingQueryPromise=this.engine?.query(r,n,i?.id),this.lastQuery=r;let t=this.parseEngineResponse(await this.executingQueryPromise);if(t.errors)throw 1===t.errors.length?this.buildQueryError(t.errors[0],e?.errorRegistry):new iv(JSON.stringify(t.errors),{clientVersion:this.config.clientVersion});if(this.loggerRustPanic)throw this.loggerRustPanic;return{data:t}}catch(t){if(t instanceof iy)throw t;if("GenericFailure"===t.code&&t.message?.startsWith("PANIC:"))throw new iw(ol(this,t.message),this.config.clientVersion);let e=this.parseRequestError(t.message);throw"string"==typeof e?t:new iv(`${e.message}
${e.backtrace}`,{clientVersion:this.config.clientVersion})}}async requestBatch(e,{transaction:t,traceparent:i}){or("requestBatch");let n=sv(e,t);await this.start();let r=await this.adapterPromise;this.lastQuery=JSON.stringify(n),this.executingQueryPromise=this.engine.query(this.lastQuery,JSON.stringify({traceparent:i}),function(e){if(e?.kind==="itx")return e.options.id}(t));let s=await this.executingQueryPromise,a=this.parseEngineResponse(s);if(a.errors)throw 1===a.errors.length?this.buildQueryError(a.errors[0],r?.errorRegistry):new iv(JSON.stringify(a.errors),{clientVersion:this.config.clientVersion});let{batchResult:o,errors:l}=a;if(Array.isArray(o))return o.map(e=>e.errors&&e.errors.length>0?this.loggerRustPanic??this.buildQueryError(e.errors[0],r?.errorRegistry):{data:e});throw l&&1===l.length?Error(l[0].error):Error(JSON.stringify(a))}buildQueryError(e,t){if(e.user_facing_error.is_panic)return new iw(ol(this,e.user_facing_error.message),this.config.clientVersion);let i=this.getExternalAdapterError(e.user_facing_error,t);return i?i.error:sE(e,this.config.clientVersion,this.config.activeProvider)}getExternalAdapterError(e,t){if("P2036"===e.error_code&&t){let i=e.meta?.id;it("number"==typeof i,"Malformed external JS error received from the engine");let n=t.consumeError(i);return it(n,"External error with reported id was not registered"),n}}async metrics(e){await this.start();let t=await this.engine.metrics(JSON.stringify(e));return"prometheus"===e.format?t:this.parseEngineResponse(t)}};function ol(e,t){return function({version:e,binaryTarget:t,title:i,description:n,engineVersion:r,database:s,query:a}){var o;let l=function(e=7500){let t=ey.map(([e,...t])=>`${e} ${t.map(e=>"string"==typeof e?e:JSON.stringify(e)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}(6e3-(a?.length??0)),u=(0,ac.default)(l).split(`
`).map(e=>e.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`),d=n?`# Description
\`\`\`
${n}
\`\`\``:"",c=function({title:e,user:t="prisma",repo:i="prisma",template:n="bug_report.yml",body:r}){return(0,af.default)({user:t,repo:i,template:n,title:e,body:r})}({title:i,body:(0,ac.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${r?.padEnd(19)}|
| Database        | ${s?.padEnd(19)}|

${d}

## Logs
\`\`\`
${u}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${a&&(o=a)?o.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,e=>`${e[0]}5`):""}
\`\`\`
`)});return`${i}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${W(c)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}({binaryTarget:e.binaryTarget,title:t,version:e.config.clientVersion,engineVersion:e.versionInfo?.commit,database:e.config.activeProvider,query:e.lastQuery})}function ou({generator:e}){return e?.previewFeatures??[]}var od=e=>({command:e}),oc=e=>e.strings.reduce((e,t,i)=>`${e}@P${i}${t}`);function of(e){try{return oh(e,"fast")}catch{return oh(e,"slow")}}function oh(e,t){return JSON.stringify(e.map(e=>(function e(t,i){if(Array.isArray(t))return t.map(t=>e(t,i));if("bigint"==typeof t)return{prisma__type:"bigint",prisma__value:t.toString()};if(nY(t))return{prisma__type:"date",prisma__value:t.toJSON()};if(nB.isDecimal(t))return{prisma__type:"decimal",prisma__value:t.toJSON()};if(Buffer.isBuffer(t))return{prisma__type:"bytes",prisma__value:t.toString("base64")};if(t instanceof ArrayBuffer||t instanceof SharedArrayBuffer||"object"==typeof t&&null!==t&&("ArrayBuffer"===t[Symbol.toStringTag]||"SharedArrayBuffer"===t[Symbol.toStringTag]))return{prisma__type:"bytes",prisma__value:Buffer.from(t).toString("base64")};if(ArrayBuffer.isView(t)){let{buffer:e,byteOffset:i,byteLength:n}=t;return{prisma__type:"bytes",prisma__value:Buffer.from(e,i,n).toString("base64")}}return"object"==typeof t&&"slow"===i?op(t):t})(e,t)))}function op(e){if("object"!=typeof e||null===e)return e;if("function"==typeof e.toJSON)return e.toJSON();if(Array.isArray(e))return e.map(om);let t={};for(let i of Object.keys(e))t[i]=om(e[i]);return t}function om(e){return"bigint"==typeof e?e.toString():op(e)}var og=/^(\s*alter\s)/i,oy=ex("prisma:client");function ob(e,t,i,n){if(!("postgresql"!==e&&"cockroachdb"!==e)&&i.length>0&&og.exec(t))throw Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var ow=({clientMethod:e,activeProvider:t})=>i=>{let n="",r;if(se(i))n=i.sql,r={values:of(i.values),__prismaRawParameters__:!0};else if(Array.isArray(i)){let[e,...t]=i;n=e,r={values:of(t||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":n=i.sql,r={values:of(i.values),__prismaRawParameters__:!0};break;case"cockroachdb":case"postgresql":case"postgres":n=i.text,r={values:of(i.values),__prismaRawParameters__:!0};break;case"sqlserver":n=oc(i),r={values:of(i.values),__prismaRawParameters__:!0};break;default:throw Error(`The ${t} provider does not support ${e}`)}return r?.values?oy(`prisma.${e}(${n}, ${r.values})`):oy(`prisma.${e}(${n})`),{query:n,parameters:r}},ov={requestArgsToMiddlewareArgs:e=>[e.strings,...e.values],middlewareArgsToRequestArgs(e){let[t,...i]=e;return new sa(t,i)}},oE={requestArgsToMiddlewareArgs:e=>[e],middlewareArgsToRequestArgs:e=>e[0]};function ox(e){return function(t,i){let n,r=(i=e)=>{try{return void 0===i||i?.kind==="itx"?n??=oP(t(i)):oP(t(i))}catch(e){return Promise.reject(e)}};return{get spec(){return i},then:(e,t)=>r().then(e,t),catch:e=>r().catch(e),finally:e=>r().finally(e),requestTransaction(e){let t=r(e);return t.requestTransaction?t.requestTransaction(e):t},[Symbol.toStringTag]:"PrismaPromise"}}}function oP(e){return"function"==typeof e.then?e:Promise.resolve(e)}var oR=tW.split(".")[0],o_={isEnabled:()=>!1,getTraceParent:()=>"00-10-10-00",dispatchEngineSpans(){},getActiveContext(){},runInChildSpan:(e,t)=>t()},oS=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(e){return this.getGlobalTracingHelper().getTraceParent(e)}dispatchEngineSpans(e){return this.getGlobalTracingHelper().dispatchEngineSpans(e)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(e,t){return this.getGlobalTracingHelper().runInChildSpan(e,t)}getGlobalTracingHelper(){let e=globalThis[`V${oR}_PRISMA_INSTRUMENTATION`],t=globalThis.PRISMA_INSTRUMENTATION;return e?.helper??t?.helper??o_}},oA=class{use(e){this._middlewares.push(e)}get(e){return this._middlewares[e]}has(e){return!!this._middlewares[e]}length(){return this._middlewares.length}constructor(){this._middlewares=[]}},oT=h(P());function oO(e){return"number"==typeof e.batchRequestIdx}function oq(e){return`(${Object.keys(e).sort().map(t=>{let i=e[t];return"object"==typeof i&&null!==i?`(${t} ${oq(i)})`:t}).join(" ")})`}var oI={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0},ok=class{constructor(e){this.tickActive=!1,this.options=e,this.batches={}}request(e){let t=this.options.batchBy(e);return t?(this.batches[t]||(this.batches[t]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((i,n)=>{this.batches[t].push({request:e,resolve:i,reject:n})})):this.options.singleLoader(e)}dispatchBatches(){for(let e in this.batches){let t=this.batches[e];delete this.batches[e],1===t.length?this.options.singleLoader(t[0].request).then(e=>{e instanceof Error?t[0].reject(e):t[0].resolve(e)}).catch(e=>{t[0].reject(e)}):(t.sort((e,t)=>this.options.batchOrder(e.request,t.request)),this.options.batchLoader(t.map(e=>e.request)).then(e=>{if(e instanceof Error)for(let i=0;i<t.length;i++)t[i].reject(e);else for(let i=0;i<t.length;i++){let n=e[i];n instanceof Error?t[i].reject(n):t[i].resolve(n)}}).catch(e=>{for(let i=0;i<t.length;i++)t[i].reject(e)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function o$(e){let t=[],i=function(e){let t={};for(let i=0;i<e.columns.length;i++)t[e.columns[i]]=null;return t}(e);for(let n=0;n<e.rows.length;n++){let r=e.rows[n],s={...i};for(let t=0;t<r.length;t++)s[e.columns[t]]=function e(t,i){if(null===i)return i;switch(t){case"bigint":return BigInt(i);case"bytes":{let{buffer:e,byteOffset:t,byteLength:n}=Buffer.from(i,"base64");return new Uint8Array(e,t,n)}case"decimal":return new nB(i);case"datetime":case"date":return new Date(i);case"time":return new Date(`1970-01-01T${i}Z`);case"bigint-array":return i.map(t=>e("bigint",t));case"bytes-array":return i.map(t=>e("bytes",t));case"decimal-array":return i.map(t=>e("decimal",t));case"datetime-array":return i.map(t=>e("datetime",t));case"date-array":return i.map(t=>e("date",t));case"time-array":return i.map(t=>e("time",t));default:return i}}(e.types[t],r[t]);t.push(s)}return t}var oN=ex("prisma:client:request_handler"),oD=class{constructor(e,t){this.logEmitter=t,this.client=e,this.dataloader=new ok({batchLoader:function(e){return t=>{let i={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?function e(t,i,n,r){if(n===i.length)return r(t);let s=t.customDataProxyFetch,a=t.requests[0].transaction;return i[n]({args:{queries:t.requests.map(e=>({model:e.modelName,operation:e.action,args:e.args})),transaction:a?{isolationLevel:"batch"===a.kind?a.isolationLevel:void 0}:void 0},__internalParams:t,query(a,o=t){let l=o.customDataProxyFetch;return o.customDataProxyFetch=sZ(s,l),e(o,i,n+1,r)}})}(i,n,0,e):e(i)}}(async({requests:e,customDataProxyFetch:t})=>{let{transaction:i,otelParentCtx:n}=e[0],r=e.map(e=>e.protocolQuery),s=this.client._tracingHelper.getTraceParent(n),a=e.some(e=>oI[e.protocolQuery.action]);return(await this.client._engine.requestBatch(r,{traceparent:s,transaction:function(e){if(e){if("batch"===e.kind)return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if("itx"===e.kind)return{kind:"itx",options:oF(e)};ii(e,"Unknown transaction kind")}}(i),containsWrite:a,customDataProxyFetch:t})).map((t,i)=>{if(t instanceof Error)return t;try{return this.mapQueryEngineResult(e[i],t)}catch(e){return e}})}),singleLoader:async e=>{let t=e.transaction?.kind==="itx"?oF(e.transaction):void 0,i=await this.client._engine.request(e.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:t,isWrite:oI[e.protocolQuery.action],customDataProxyFetch:e.customDataProxyFetch});return this.mapQueryEngineResult(e,i)},batchBy:e=>e.transaction?.id?`transaction-${e.transaction.id}`:function(e){if("findUnique"!==e.action&&"findUniqueOrThrow"!==e.action)return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(oq(e.query.arguments)),t.push(oq(e.query.selection)),t.join("")}(e.protocolQuery),batchOrder:(e,t)=>e.transaction?.kind==="batch"&&t.transaction?.kind==="batch"?e.transaction.index-t.transaction.index:0})}async request(e){try{return await this.dataloader.request(e)}catch(a){let{clientMethod:t,callsite:i,transaction:n,args:r,modelName:s}=e;this.handleAndLogRequestError({error:a,clientMethod:t,callsite:i,transaction:n,args:r,modelName:s,globalOmit:e.globalOmit})}}mapQueryEngineResult({dataPath:e,unpacker:t},i){let n=i?.data,r=this.unpack(n,e,t);return process.env.PRISMA_CLIENT_GET_TIME?{data:r}:r}handleAndLogRequestError(e){try{this.handleRequestError(e)}catch(t){throw this.logEmitter&&this.logEmitter.emit("error",{message:t.message,target:e.clientMethod,timestamp:new Date}),t}}handleRequestError({error:e,clientMethod:t,callsite:i,transaction:n,args:r,modelName:s,globalOmit:a}){if(oN(e),oO(e)&&n?.kind==="batch"&&e.batchRequestIdx!==n.index)throw e;e instanceof ib&&("P2009"===e.code||"P2012"===e.code)&&rC({args:r,errors:[function e(t){if("Union"===t.kind)return{kind:"Union",errors:t.errors.map(e)};if(Array.isArray(t.selectionPath)){let[,...e]=t.selectionPath;return{...t,selectionPath:e}}return t}(e.meta)],callsite:i,errorFormat:this.client._errorFormat,originalMethod:t,clientVersion:this.client._clientVersion,globalOmit:a});let o=e.message;if(i&&(o=rt({callsite:i,originalMethod:t,isPanic:e.isPanic,showColors:"pretty"===this.client._errorFormat,message:o})),o=this.sanitizeMessage(o),e.code){let t=s?{modelName:s,...e.meta}:e.meta;throw new ib(o,{code:e.code,clientVersion:this.client._clientVersion,meta:t,batchRequestIdx:e.batchRequestIdx})}if(e.isPanic)throw new iw(o,this.client._clientVersion);if(e instanceof iv)throw new iv(o,{clientVersion:this.client._clientVersion,batchRequestIdx:e.batchRequestIdx});if(e instanceof iy)throw new iy(o,this.client._clientVersion);if(e instanceof iw)throw new iw(o,this.client._clientVersion);throw e.clientVersion=this.client._clientVersion,e}sanitizeMessage(e){return this.client._errorFormat&&"pretty"!==this.client._errorFormat?(0,oT.default)(e):e}unpack(e,t,i){if(!e||(e.data&&(e=e.data),!e))return e;let n=Object.keys(e)[0],r=sj(Object.values(e)[0],t.filter(e=>"select"!==e&&"include"!==e)),s="queryRaw"===n?o$(r):nQ(r);return i?i(s):s}get[Symbol.toStringTag](){return"RequestHandler"}};function oF(e){return{id:e.id,payload:e.payload}}var oU=h(A()),oV=class extends Error{constructor(e){super(e+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};ip(oV,"PrismaClientConstructorValidationError");var oj=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],oC=["pretty","colorless","minimal"],oL=["info","query","warn","error"],oM={datasources:(e,{datasourceNames:t})=>{if(e){if("object"!=typeof e||Array.isArray(e))throw new oV(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[i,n]of Object.entries(e)){if(!t.includes(i)){let e=oG(i,t)||` Available datasources: ${t.join(", ")}`;throw new oV(`Unknown datasource ${i} provided to PrismaClient constructor.${e}`)}if("object"!=typeof n||Array.isArray(n))throw new oV(`Invalid value ${JSON.stringify(e)} for datasource "${i}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&"object"==typeof n)for(let[t,r]of Object.entries(n)){if("url"!==t)throw new oV(`Invalid value ${JSON.stringify(e)} for datasource "${i}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if("string"!=typeof r)throw new oV(`Invalid value ${JSON.stringify(r)} for datasource "${i}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&"client"===tH(t.generator))throw new oV('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(null!==e){if(void 0===e)throw new oV('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!ou(t).includes("driverAdapters"))throw new oV('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if("binary"===tH(t.generator))throw new oV('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')}},datasourceUrl:e=>{if("u">typeof e&&"string"!=typeof e)throw new oV(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if("string"!=typeof e)throw new oV(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!oC.includes(e)){let t=oG(e,oC);throw new oV(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(e){if(!Array.isArray(e))throw new oV(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);for(let i of e){t(i);let e={level:t,emit:e=>{let t=["stdout","event"];if(!t.includes(e)){let i=oG(e,t);throw new oV(`Invalid value ${JSON.stringify(e)} for "emit" in logLevel provided to PrismaClient constructor.${i}`)}}};if(i&&"object"==typeof i)for(let[t,n]of Object.entries(i))if(e[t])e[t](n);else throw new oV(`Invalid property ${t} for "log" provided to PrismaClient constructor`)}}function t(e){if("string"==typeof e&&!oL.includes(e)){let t=oG(e,oL);throw new oV(`Invalid log level "${e}" provided to PrismaClient constructor.${t}`)}}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(null!=t&&t<=0)throw new oV(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let i=e.timeout;if(null!=i&&i<=0)throw new oV(`Invalid value ${i} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if("object"!=typeof e)throw new oV('"omit" option is expected to be an object.');if(null===e)throw new oV('"omit" option can not be `null`');let i=[];for(let[n,r]of Object.entries(e)){let e=function(e,t){return oB(t.models,e)??oB(t.types,e)}(n,t.runtimeDataModel);if(!e){i.push({kind:"UnknownModel",modelKey:n});continue}for(let[t,s]of Object.entries(r)){let r=e.fields.find(e=>e.name===t);if(!r){i.push({kind:"UnknownField",modelKey:n,fieldName:t});continue}if(r.relationName){i.push({kind:"RelationInOmit",modelKey:n,fieldName:t});continue}"boolean"!=typeof s&&i.push({kind:"InvalidFieldValue",modelKey:n,fieldName:t})}}if(i.length>0)throw new oV(function(e,t){let i=rV(e);for(let e of t)switch(e.kind){case"UnknownModel":i.arguments.getField(e.modelKey)?.markAsError(),i.addErrorMessage(()=>`Unknown model name: ${e.modelKey}.`);break;case"UnknownField":i.arguments.getDeepField([e.modelKey,e.fieldName])?.markAsError(),i.addErrorMessage(()=>`Model "${e.modelKey}" does not have a field named "${e.fieldName}".`);break;case"RelationInOmit":i.arguments.getDeepField([e.modelKey,e.fieldName])?.markAsError(),i.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":i.arguments.getDeepFieldValue([e.modelKey,e.fieldName])?.markAsError(),i.addErrorMessage(()=>"Omit field option value must be a boolean.")}let{message:n,args:r}=rj(i,"colorless");return`Error validating "omit" option:

${r}

${n}`}(e,i))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if("object"!=typeof e)throw new oV(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[i]of Object.entries(e))if(!t.includes(i)){let e=oG(i,t);throw new oV(`Invalid property ${JSON.stringify(i)} for "__internal" provided to PrismaClient constructor.${e}`)}}};function oG(e,t){if(0===t.length||"string"!=typeof e)return"";let i=function(e,t){if(0===t.length)return null;let i=t.map(t=>({value:t,distance:(0,oU.default)(e,t)}));i.sort((e,t)=>e.distance<t.distance?-1:1);let n=i[0];return n.distance<3?n.value:null}(e,t);return i?` Did you mean "${i}"?`:""}function oB(e,t){let i=Object.keys(e).find(e=>nJ(e)===t);if(i)return e[i]}var oQ=ex("prisma:client");"object"==typeof globalThis&&(globalThis.NODE_CLIENT=!0);var oz={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},oJ=Symbol.for("prisma.client.transaction.id"),oW={id:0,nextId(){return++this.id}};function oH(e){class t{constructor(t){this._originalClient=this,this._middlewares=new oA,this._createPrismaPromise=ox(),this.$metrics=new r4(this),this.$extends=sz,function({postinstall:e,ciName:t,clientVersion:i}){if(sX("checkPlatformCaching:postinstall",e),sX("checkPlatformCaching:ciName",t),!0===e&&t&&t in s0){let e=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${s0[t]}-build`;throw console.error(e),new iy(e,i)}}(e=t?.__internal?.configOverride?.(e)??e),t&&function(e,t){for(let[i,n]of Object.entries(e)){if(!oj.includes(i)){let e=oG(i,oj);throw new oV(`Unknown property ${i} provided to PrismaClient constructor.${e}`)}oM[i](n,t)}if(e.datasourceUrl&&e.datasources)throw new oV('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}(t,e);let i=new sn.EventEmitter().on("error",()=>{});this._extensions=rB.empty(),this._previewFeatures=ou(e),this._clientVersion=e.clientVersion??"6.8.2",this._activeProvider=e.activeProvider,this._globalOmit=t?.omit,this._tracingHelper=new oS;let n=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&ss.default.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&ss.default.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},r;if(t?.adapter){r=t.adapter;let i="postgresql"===e.activeProvider?"postgres":e.activeProvider;if(r.provider!==i)throw new iy(`The Driver Adapter \`${r.adapterName}\`, based on \`${r.provider}\`, is not compatible with the provider \`${i}\` specified in the Prisma schema.`,this._clientVersion);if(t.datasources||void 0!==t.datasourceUrl)throw new iy("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let s=!r&&n&&iu(n,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{var a,o;let n=t??{},l=n.__internal??{},u=!0===l.debug;u&&ex.enable("prisma:client");let d=ss.default.resolve(e.dirname,e.relativePath);sr.default.existsSync(d)||(d=e.dirname),oQ("dirname",e.dirname),oQ("relativePath",e.relativePath),oQ("cwd",d);let c=l.engine||{};if(n.errorFormat?this._errorFormat=n.errorFormat:this._errorFormat="minimal",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:d,dirname:e.dirname,enableDebugLogs:u,allowTriggerPanic:c.allowTriggerPanic,prismaPath:c.binaryPath??void 0,engineEndpoint:c.endpoint,generator:e.generator,showColors:"pretty"===this._errorFormat,logLevel:n.log&&(a=n.log,"string"==typeof a?a:a.reduce((e,t)=>{let i="string"==typeof t?t:t.level;return"query"===i?e:e&&("info"===t||"info"===e)?"info":i},void 0)),logQueries:n.log&&!!("string"==typeof n.log?"query"===n.log:n.log.find(e=>"string"==typeof e?"query"===e:"query"===e.level)),env:s?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:(o=e.datasourceNames,n?n.datasources?n.datasources:n.datasourceUrl?{[o[0]]:{url:n.datasourceUrl}}:{}:{}),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:n.transactionOptions?.maxWait??2e3,timeout:n.transactionOptions?.timeout??5e3,isolationLevel:n.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:r},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:aE,getBatchRequestPayload:sv,prismaGraphQLToJSError:sE,PrismaClientUnknownRequestError:iv,PrismaClientInitializationError:iy,PrismaClientKnownRequestError:ib,debug:ex("prisma:client:accelerateEngine"),engineVersion:st.version,clientVersion:e.clientVersion}},oQ("clientVersion",e.clientVersion),this._engine=function({copyEngine:e=!0},t){let i;try{i=aE({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let n=!!(i?.startsWith("prisma://")||t0(i));e&&n&&ig("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"),tH(t.generator);let r=n||!e,s=!!t.adapter;if(r&&s)throw new iE((e?i?.startsWith("prisma://")?["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]).join(`
`),{clientVersion:t.clientVersion});return r?new a5(t):new oo(t)}(e,this._engineConfig),this._requestHandler=new oD(this,i),n.log)for(let e of n.log){let t="string"==typeof e?e:"stdout"===e.emit?e.level:null;t&&this.$on(t,e=>{t4.log(`${t4.tags[t]??""}`,e.message||e.query)})}}catch(e){throw e.clientVersion=this._clientVersion,e}return this._appliedParent=sQ(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(e){this._middlewares.use(e)}$on(e,t){return"beforeExit"===e?this._engine.onBeforeExit(t):e&&this._engineConfig.logEmitter.on(e,t),this}$connect(){try{return this._engine.start()}catch(e){throw e.clientVersion=this._clientVersion,e}}async $disconnect(){try{await this._engine.stop()}catch(e){throw e.clientVersion=this._clientVersion,e}finally{ey.length=0}}$executeRawInternal(e,t,i,n){let r=this._activeProvider;return this._request({action:"executeRaw",args:i,transaction:e,clientMethod:t,argsMapper:ow({clientMethod:t,activeProvider:r}),callsite:sk(this._errorFormat),dataPath:[],middlewareArgsMapper:n})}$executeRaw(e,...t){return this._createPrismaPromise(i=>{if(void 0!==e.raw||void 0!==e.sql){let[n,r]=oK(e,t);return ob(this._activeProvider,n.text,n.values,Array.isArray(e)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(i,"$executeRaw",n,r)}throw new iE("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(e,...t){return this._createPrismaPromise(i=>(ob(this._activeProvider,e,t,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(i,"$executeRawUnsafe",[e,...t])))}$runCommandRaw(t){if("mongodb"!==e.activeProvider)throw new iE(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(e=>this._request({args:t,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:od,callsite:sk(this._errorFormat),transaction:e}))}async $queryRawInternal(e,t,i,n){let r=this._activeProvider;return this._request({action:"queryRaw",args:i,transaction:e,clientMethod:t,argsMapper:ow({clientMethod:t,activeProvider:r}),callsite:sk(this._errorFormat),dataPath:[],middlewareArgsMapper:n})}$queryRaw(e,...t){return this._createPrismaPromise(i=>{if(void 0!==e.raw||void 0!==e.sql)return this.$queryRawInternal(i,"$queryRaw",...oK(e,t));throw new iE("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(e){return this._createPrismaPromise(t=>{if(!this._hasPreviewFlag("typedSql"))throw new iE("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(t,"$queryRawTyped",e)})}$queryRawUnsafe(e,...t){return this._createPrismaPromise(i=>this.$queryRawInternal(i,"$queryRawUnsafe",[e,...t]))}_transactionWithArray({promises:e,options:t}){var i;let n=oW.nextId(),r=function(e,t=()=>{}){let i,n=new Promise(e=>i=e);return{then:r=>(0==--e&&i(t()),r?.(n))}}(e.length);return 0===(i=e.map((e,i)=>{if(e?.[Symbol.toStringTag]!=="PrismaPromise")throw Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let s=t?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel;return e.requestTransaction?.({kind:"batch",id:n,index:i,isolationLevel:s,lock:r})??e})).length?Promise.resolve([]):new Promise((e,t)=>{let n=Array(i.length),r=null,s=!1,a=0,o=()=>{s||++a===i.length&&(s=!0,r?t(r):e(n))},l=e=>{s||(s=!0,t(e))};for(let e=0;e<i.length;e++)i[e].then(t=>{n[e]=t,o()},t=>{if(!oO(t)){l(t);return}t.batchRequestIdx===e?l(t):(r||(r=t),o())})})}async _transactionWithCallback({callback:e,options:t}){let i={traceparent:this._tracingHelper.getTraceParent()},n={maxWait:t?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:t?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:t?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},r=await this._engine.transaction("start",i,n),s;try{let t={kind:"itx",...r};s=await e(this._createItxClient(t)),await this._engine.transaction("commit",i,r)}catch(e){throw await this._engine.transaction("rollback",i,r).catch(()=>{}),e}return s}_createItxClient(e){return sy(sQ(sy(this[sB]?this[sB]:this,[sf("_appliedParent",()=>this._appliedParent._createItxClient(e)),sf("_createPrismaPromise",()=>ox(e)),sf(oJ,()=>e.id)])),[sw(sH)])}$transaction(e,t){let i;return i="function"==typeof e?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?()=>{throw Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:()=>this._transactionWithCallback({callback:e,options:t}):()=>this._transactionWithArray({promises:e,options:t}),this._tracingHelper.runInChildSpan({name:"transaction",attributes:{method:"$transaction"}},i)}_request(e){e.otelParentCtx=this._tracingHelper.getActiveContext();let t=e.middlewareArgsMapper??oz,i={args:t.requestArgsToMiddlewareArgs(e.args),dataPath:e.dataPath,runInTransaction:!!e.transaction,action:e.action,model:e.model},n={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:i.action,model:i.model,name:i.model?`${i.model}.${i.action}`:i.action}}},r=-1,s=async i=>{let a=this._middlewares.get(++r);if(a)return this._tracingHelper.runInChildSpan(n.middleware,e=>a(i,t=>(e?.end(),s(t))));let{runInTransaction:o,args:l,...u}=i,d={...e,...u};l&&(d.args=t.middlewareArgsToRequestArgs(l)),void 0!==e.transaction&&!1===o&&delete d.transaction;let c=await function(e,t){let{jsModelName:i,action:n,clientMethod:r}=t;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(i??"$none",i?n:r);return function e(t,i,n,r=0){return t._createPrismaPromise(s=>{let a=i.customDataProxyFetch;return"transaction"in i&&void 0!==s&&(i.transaction?.kind==="batch"&&i.transaction.lock.then(),i.transaction=s),r===n.length?t._executeRequest(i):n[r]({model:i.model,operation:i.model?i.action:i.clientMethod,args:function(e){if(e instanceof sa)return new sa(e.strings,e.values);if(se(e))return new r5(e.sql,e.values);if(Array.isArray(e)){let t=[e[0]];for(let i=1;i<e.length;i++)t[i]=sK(e[i]);return t}let t={};for(let i in e)t[i]=sK(e[i]);return t}(i.args??{}),__internalParams:i,query:(s,o=i)=>{let l=o.customDataProxyFetch;return o.customDataProxyFetch=sZ(a,l),o.args=s,e(t,o,n,r+1)}})})}(e,t,s)}(this,d);return d.model?function({result:e,modelName:t,args:i,extensions:n,runtimeDataModel:r,globalOmit:s}){return n.isEmpty()||null==e||"object"!=typeof e||!r.models[t]?e:sJ({result:e,args:i??{},modelName:t,runtimeDataModel:r,visitor:(e,t,i)=>{let r=rL(t);return function({result:e,modelName:t,select:i,omit:n,extensions:r}){let s=r.getAllComputedFields(t);if(!s)return e;let a=[],o=[];for(let t of Object.values(s)){if(n){if(n[t.name])continue;let e=t.needs.filter(e=>n[e]);e.length>0&&o.push(sw(e))}else if(i){if(!i[t.name])continue;let e=t.needs.filter(e=>!i[e]);e.length>0&&o.push(sw(e))}(function(e,t){return t.every(t=>Object.prototype.hasOwnProperty.call(e,t))})(e,t.needs)&&a.push(function(e,t){return sh(sf(e.name,()=>e.compute(t)))}(t,sy(e,a)))}return a.length>0||o.length>0?sy(e,[...a,...o]):e}({result:e,modelName:r,select:i.select,omit:i.select?void 0:{...s?.[r],...i.omit},extensions:n})}})}({result:c,modelName:d.model,args:d.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):c};return this._tracingHelper.runInChildSpan(n.operation,()=>new si.AsyncResource("prisma-client-request").runInAsyncScope(()=>s(i)))}async _executeRequest({args:e,clientMethod:t,dataPath:i,callsite:n,action:r,model:s,argsMapper:a,transaction:o,unpacker:l,otelParentCtx:u,customDataProxyFetch:d}){try{e=a?a(e):e;let c=this._tracingHelper.runInChildSpan({name:"serialize"},()=>rX({modelName:s,runtimeDataModel:this._runtimeDataModel,action:r,args:e,clientMethod:t,callsite:n,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return ex.enabled("prisma:client")&&(oQ("Prisma Client call:"),oQ(`prisma.${t}(${function(e){if(void 0===e)return"";let t=rV(e);return new ra(0,{colors:ru}).write(t).toString()}(e)})`),oQ("Generated request:"),oQ(JSON.stringify(c,null,2)+`
`)),o?.kind==="batch"&&await o.lock,this._requestHandler.request({protocolQuery:c,modelName:s,action:r,clientMethod:t,dataPath:i,callsite:n,args:e,extensions:this._extensions,transaction:o,unpacker:l,otelParentCtx:u,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:d})}catch(e){throw e.clientVersion=this._clientVersion,e}}_hasPreviewFlag(e){return!!this._engineConfig.previewFeatures?.includes(e)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}}return t}function oK(e,t){return Array.isArray(e)&&Array.isArray(e.raw)?[new sa(e,t),ov]:[e,oE]}var oY=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function oZ(e){return new Proxy(e,{get(e,t){if(t in e)return e[t];if(!oY.has(t))throw TypeError(`Invalid enum value: ${String(t)}`)}})}function oX(e){iu(e,{conflictCheck:"warn"})}},27914:(e,t,i)=>{i.d(t,{db:()=>r});var n=i(16343);let r=globalThis.prisma||new n.PrismaClient}};