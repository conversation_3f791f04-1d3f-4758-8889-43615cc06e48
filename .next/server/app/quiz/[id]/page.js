(()=>{var e={};e.id=200,e.ids=[200],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},31421:e=>{"use strict";e.exports=require("node:child_process")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},1708:e=>{"use strict";e.exports=require("node:process")},7066:e=>{"use strict";e.exports=require("node:tty")},57975:e=>{"use strict";e.exports=require("node:util")},8218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var n=r(70260),s=r(28203),i=r(25155),o=r.n(i),l=r(67292),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(t,a);let u=["",{children:["quiz",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4927)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/quiz/[id]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/quiz/[id]/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/quiz/[id]/page",pathname:"/quiz/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},14435:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,59607,23)),Promise.resolve().then(r.bind(r,29353))},28507:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,28531,23)),Promise.resolve().then(r.bind(r,32137))},86256:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(25488)._(r(40568));function s(e,t){var r;let s={};"function"==typeof e&&(s.loader=e);let i={...s,...t};return(0,n.default)({...i,modules:null==(r=i.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3400:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},65771:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}});let n=r(54639);function s(e){let{reason:t,children:r}=e;throw new n.BailoutToCSRError(t)}},40568:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(45512),s=r(58009),i=r(65771),o=r(86054);function l(e){return{default:e&&"default"in e?e.default:e}}let a={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},u=function(e){let t={...a,...e},r=(0,s.lazy)(()=>t.loader().then(l)),u=t.loading;function d(e){let l=u?(0,n.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,a=!t.ssr||!!t.loading,d=a?s.Suspense:s.Fragment,c=t.ssr?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.PreloadChunks,{moduleIds:t.modules}),(0,n.jsx)(r,{...e})]}):(0,n.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(d,{...a?{fallback:l}:{},children:c})}return d.displayName="LoadableComponent",d}},86054:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return l}});let n=r(45512),s=r(55740),i=r(29294),o=r(3400);function l(e){let{moduleIds:t}=e,r=i.workAsyncStorage.getStore();if(void 0===r)return null;let l=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;l.push(...t)}}return 0===l.length?null:(0,n.jsx)(n.Fragment,{children:l.map(e=>{let t=r.assetPrefix+"/_next/"+(0,o.encodeURIPath)(e);return e.endsWith(".css")?(0,n.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,s.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},32137:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(45512);r(58009);var s=r(86256);let i=r.n(s)()(async()=>{},{loadableGenerated:{modules:["components/quiz/QuizClient.tsx -> ./QuizRenderer"]},ssr:!1}),o=({quiz:e})=>(0,n.jsx)(i,{quiz:e})},59607:(e,t,r)=>{let{createProxy:n}=r(73439);e.exports=n("/Users/<USER>/Documents/augment-projects/hacking-quiz/node_modules/next/dist/client/app-dir/link.js")},4927:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(62740),s=r(31831),i=r(59607),o=r.n(i),l=r(51825),a=r(37702),u=r(27914),d=r(13797),c=r(29353);async function p({params:e}){let t=await (0,l.getServerSession)(a.N),{id:r}=await e,i=await u.db.quiz.findUnique({where:{id:r},include:{creator:{select:{name:!0}},questions:!0,questionPools:{include:{questions:!0}},selectionRules:!0}});i&&(i.isPublished||i.creatorId===t?.user.id)||(0,s.notFound)();let p={quiz:{$schema:"https://quizflow.org/schemas/quizflow_schema_v1.1.json",metadata:{format_version:i.formatVersion,quiz_id:i.quizId,title:i.title,description:i.description||void 0,author:i.creator?.name||i.author||void 0,creation_date:i.creationDate.toISOString(),tags:i.tags,passing_score_percentage:i.passingScore||void 0,time_limit_minutes:i.timeLimit||void 0,markup_format:i.markupFormat,locale:i.locale},questions:i.questions.map(e=>({...e,id:void 0,quizId:void 0,createdAt:void 0,updatedAt:void 0,questionPoolId:void 0})),question_pools:i.questionPools.length>0?i.questionPools.map(e=>({pool_id:e.poolId,title:e.title||void 0,description:e.description||void 0,questions:e.questions.map(e=>({...e,id:void 0,quizId:void 0,createdAt:void 0,updatedAt:void 0,questionPoolId:void 0}))})):void 0,selection_rules:i.selectionRules.length>0?i.selectionRules.map(e=>({pool_id:e.poolId,select_count:e.selectCount,randomize:e.randomize,shuffle_order:e.shuffleOrder})):void 0}};return(0,n.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,n.jsx)("header",{className:"border-b",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,n.jsx)("div",{className:"flex items-center gap-6",children:(0,n.jsx)(o(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"})}),(0,n.jsx)("div",{className:"flex items-center gap-4",children:t?(0,n.jsx)(d.$,{asChild:!0,children:(0,n.jsx)(o(),{href:"/dashboard",children:"Dashboard"})}):(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(d.$,{variant:"outline",asChild:!0,children:(0,n.jsx)(o(),{href:"/auth/login",children:"Sign In"})}),(0,n.jsx)(d.$,{asChild:!0,children:(0,n.jsx)(o(),{href:"/auth/register",children:"Sign Up"})})]})})]})}),(0,n.jsx)("main",{className:"flex-1 py-8",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsxs)(o(),{href:"/explore",className:"text-primary hover:underline flex items-center",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Explore"]})}),(0,n.jsx)(c.default,{quiz:p})]})}),(0,n.jsx)("footer",{className:"py-8 border-t",children:(0,n.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,n.jsxs)("p",{className:"text-muted-foreground",children:["\xa9 ",new Date().getFullYear()," QuizFlow. All rights reserved."]})})})]})}},29353:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/QuizClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/QuizClient.tsx","default")},13797:(e,t,r)=>{"use strict";r.d(t,{$:()=>m});var n=r(62740),s=r(76301);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var o=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){let e,o;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,a=function(e,t){let r={...t};for(let n in t){let s=e[n],i=t[n];/^on[A-Z]/.test(n)?s&&i?r[n]=(...e)=>{let t=i(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...i}:"className"===n&&(r[n]=[s,i].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(a.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}(t,l):l),s.cloneElement(r,a)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:i,...o}=e,l=s.Children.toArray(i),u=l.find(a);if(u){let e=u.props.children,i=l.map(t=>t!==u?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...o,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,i):null})}return(0,n.jsx)(t,{...o,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}("Slot"),l=Symbol("radix.slottable");function a(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var u=r(13673);let d=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,c=u.$;var p=r(73300);let f=((e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return c(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:i}=t,o=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let o=d(t)||d(n);return s[e][o]}),l=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return c(e,o,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...l}[t]):({...i,...l})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...i},l)=>{let a=s?o:"button";return(0,n.jsx)(a,{className:(0,p.cn)(f({variant:t,size:r,className:e})),ref:l,...i})});m.displayName="Button"},73300:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>o,cn:()=>i});var n=r(13673),s=r(47317);function i(...e){return(0,s.QP)((0,n.$)(e))}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,263,3,367,85,914,30],()=>r(8218));module.exports=n})();