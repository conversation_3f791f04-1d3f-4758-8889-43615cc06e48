(()=>{var e={};e.id=259,e.ids=[259],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},31421:e=>{"use strict";e.exports=require("node:child_process")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},1708:e=>{"use strict";e.exports=require("node:process")},7066:e=>{"use strict";e.exports=require("node:tty")},57975:e=>{"use strict";e.exports=require("node:util")},12618:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{DELETE:()=>x,GET:()=>c,PATCH:()=>p});var i=s(42706),o=s(28203),u=s(45994),n=s(39187),a=s(51825),d=s(37702),l=s(27914);async function c(e,{params:r}){try{let e=await (0,a.getServerSession)(d.N),{id:s,ruleId:t}=await r,i=await l.db.selectionRule.findUnique({where:{id:t,quizId:s}});if(!i)return n.NextResponse.json({message:"Selection rule not found"},{status:404});let o=await l.db.quiz.findUnique({where:{id:s}});if(!o)return n.NextResponse.json({message:"Quiz not found"},{status:404});if(!o.isPublished&&(!e||o.creatorId!==e.user.id))return n.NextResponse.json({message:"Unauthorized"},{status:401});return n.NextResponse.json(i)}catch(e){return console.error("Error fetching selection rule:",e),n.NextResponse.json({message:"An error occurred while fetching the selection rule"},{status:500})}}async function p(e,{params:r}){try{let s=await (0,a.getServerSession)(d.N);if(!s)return n.NextResponse.json({message:"Unauthorized"},{status:401});let{id:t,ruleId:i}=await r,o=await e.json();if(!await l.db.quiz.findUnique({where:{id:t,creatorId:s.user.id}}))return n.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await l.db.selectionRule.findUnique({where:{id:i,quizId:t}}))return n.NextResponse.json({message:"Selection rule not found"},{status:404});if(o.poolId){let e=await l.db.questionPool.findUnique({where:{id:o.poolId,quizId:t}});if(!e)return n.NextResponse.json({message:"Pool not found"},{status:404});o.poolId=e.poolId}let u=await l.db.selectionRule.update({where:{id:i},data:o});return n.NextResponse.json(u)}catch(e){return console.error("Error updating selection rule:",e),n.NextResponse.json({message:"An error occurred while updating the selection rule"},{status:500})}}async function x(e,{params:r}){try{let e=await (0,a.getServerSession)(d.N);if(!e)return n.NextResponse.json({message:"Unauthorized"},{status:401});let{id:s,ruleId:t}=await r;if(!await l.db.quiz.findUnique({where:{id:s,creatorId:e.user.id}}))return n.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await l.db.selectionRule.findUnique({where:{id:t,quizId:s}}))return n.NextResponse.json({message:"Selection rule not found"},{status:404});return await l.db.selectionRule.delete({where:{id:t}}),n.NextResponse.json({message:"Selection rule deleted successfully"},{status:200})}catch(e){return console.error("Error deleting selection rule:",e),n.NextResponse.json({message:"An error occurred while deleting the selection rule"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/rules/[ruleId]/route",pathname:"/api/quizzes/[id]/rules/[ruleId]",filename:"route",bundlePath:"app/api/quizzes/[id]/rules/[ruleId]/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/rules/[ruleId]/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:q,workUnitAsyncStorage:w,serverHooks:h}=m;function g(){return(0,u.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:w})}},96487:()=>{},78335:()=>{},37702:(e,r,s)=>{"use strict";s.d(r,{N:()=>l});var t=s(46814),i=s(91642),o=s(28053),u=s(7553),n=s(5486),a=s.n(n),d=s(27914);let l={adapter:(0,t.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,o.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,u.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await d.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await a().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let s=await d.db.user.findFirst({where:{email:e.email||""}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,263,452,914],()=>s(12618));module.exports=t})();