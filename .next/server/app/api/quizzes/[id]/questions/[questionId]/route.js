(()=>{var e={};e.id=71,e.ids=[71],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},31421:e=>{"use strict";e.exports=require("node:child_process")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},1708:e=>{"use strict";e.exports=require("node:process")},7066:e=>{"use strict";e.exports=require("node:tty")},57975:e=>{"use strict";e.exports=require("node:util")},59077:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>x,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{DELETE:()=>q,GET:()=>c,PATCH:()=>l});var i=s(42706),o=s(28203),n=s(45994),u=s(39187),a=s(51825),d=s(37702),p=s(27914);async function c(e,{params:t}){try{let e=await (0,a.getServerSession)(d.N),{id:s,questionId:r}=await t,i=await p.db.question.findUnique({where:{id:r,quizId:s}});if(!i)return u.NextResponse.json({message:"Question not found"},{status:404});let o=await p.db.quiz.findUnique({where:{id:s}});if(!o)return u.NextResponse.json({message:"Quiz not found"},{status:404});if(!o.isPublished&&(!e||o.creatorId!==e.user.id))return u.NextResponse.json({message:"Unauthorized"},{status:401});return u.NextResponse.json(i)}catch(e){return console.error("Error fetching question:",e),u.NextResponse.json({message:"An error occurred while fetching the question"},{status:500})}}async function l(e,{params:t}){try{let s=await (0,a.getServerSession)(d.N);if(!s)return u.NextResponse.json({message:"Unauthorized"},{status:401});let{id:r,questionId:i}=await t,o=await e.json();if(!await p.db.quiz.findUnique({where:{id:r,creatorId:s.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await p.db.question.findUnique({where:{id:i,quizId:r}}))return u.NextResponse.json({message:"Question not found"},{status:404});let n={...o};for(let e of(n.text&&"string"!=typeof n.text&&(n.text=JSON.stringify(n.text)),["options","correctAnswer","correctAnswers","stems","correctPairs","textTemplate","blanks"]))n[e]&&"string"!=typeof n[e]&&(n[e]=JSON.stringify(n[e]));let c=await p.db.question.update({where:{id:i},data:n});return u.NextResponse.json(c)}catch(e){return console.error("Error updating question:",e),u.NextResponse.json({message:"An error occurred while updating the question"},{status:500})}}async function q(e,{params:t}){try{let e=await (0,a.getServerSession)(d.N);if(!e)return u.NextResponse.json({message:"Unauthorized"},{status:401});let{id:s,questionId:r}=await t;if(!await p.db.quiz.findUnique({where:{id:s,creatorId:e.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await p.db.question.findUnique({where:{id:r,quizId:s}}))return u.NextResponse.json({message:"Question not found"},{status:404});return await p.db.question.delete({where:{id:r}}),u.NextResponse.json({message:"Question deleted successfully"},{status:200})}catch(e){return console.error("Error deleting question:",e),u.NextResponse.json({message:"An error occurred while deleting the question"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/questions/[questionId]/route",pathname:"/api/quizzes/[id]/questions/[questionId]",filename:"route",bundlePath:"app/api/quizzes/[id]/questions/[questionId]/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/questions/[questionId]/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:w}=x;function f(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{},37702:(e,t,s)=>{"use strict";s.d(t,{N:()=>p});var r=s(46814),i=s(91642),o=s(28053),n=s(7553),u=s(5486),a=s.n(u),d=s(27914);let p={adapter:(0,r.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,o.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,n.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await d.db.user.findUnique({where:{email:e.email}});return t&&t.password&&await a().compare(e.password,t.password)?{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}:null}})],callbacks:{session:async({session:e,token:t})=>(t&&(e.user.id=t.id,e.user.name=t.name,e.user.email=t.email,e.user.image=t.picture,e.user.role=t.role),e),async jwt({token:e,user:t}){let s=await d.db.user.findFirst({where:{email:e.email||""}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(t&&(e.id=t.id),e)}},secret:process.env.NEXTAUTH_SECRET}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,263,452,914],()=>s(59077));module.exports=r})();