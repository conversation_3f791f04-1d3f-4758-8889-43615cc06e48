(()=>{var e={};e.id=563,e.ids=[563],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},31421:e=>{"use strict";e.exports=require("node:child_process")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},1708:e=>{"use strict";e.exports=require("node:process")},7066:e=>{"use strict";e.exports=require("node:tty")},57975:e=>{"use strict";e.exports=require("node:util")},89970:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>g,routeModule:()=>x,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{DELETE:()=>q,GET:()=>l,PATCH:()=>c});var i=s(42706),o=s(28203),n=s(45994),u=s(39187),a=s(51825),d=s(37702),p=s(27914);async function l(e,{params:r}){try{let e=await (0,a.getServerSession)(d.N),{id:s,poolId:t}=await r,i=await p.db.questionPool.findUnique({where:{id:t,quizId:s},include:{questions:!0}});if(!i)return u.NextResponse.json({message:"Pool not found"},{status:404});let o=await p.db.quiz.findUnique({where:{id:s}});if(!o)return u.NextResponse.json({message:"Quiz not found"},{status:404});if(!o.isPublished&&(!e||o.creatorId!==e.user.id))return u.NextResponse.json({message:"Unauthorized"},{status:401});return u.NextResponse.json(i)}catch(e){return console.error("Error fetching pool:",e),u.NextResponse.json({message:"An error occurred while fetching the pool"},{status:500})}}async function c(e,{params:r}){try{let s=await (0,a.getServerSession)(d.N);if(!s)return u.NextResponse.json({message:"Unauthorized"},{status:401});let{id:t,poolId:i}=await r,o=await e.json();if(!await p.db.quiz.findUnique({where:{id:t,creatorId:s.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await p.db.questionPool.findUnique({where:{id:i,quizId:t}}))return u.NextResponse.json({message:"Pool not found"},{status:404});let n=await p.db.questionPool.update({where:{id:i},data:o});return u.NextResponse.json(n)}catch(e){return console.error("Error updating pool:",e),u.NextResponse.json({message:"An error occurred while updating the pool"},{status:500})}}async function q(e,{params:r}){try{let e=await (0,a.getServerSession)(d.N);if(!e)return u.NextResponse.json({message:"Unauthorized"},{status:401});let{id:s,poolId:t}=await r;if(!await p.db.quiz.findUnique({where:{id:s,creatorId:e.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});let i=await p.db.questionPool.findUnique({where:{id:t,quizId:s}});if(!i)return u.NextResponse.json({message:"Pool not found"},{status:404});return await p.db.selectionRule.deleteMany({where:{quizId:s,poolId:i.poolId}}),await p.db.questionPool.delete({where:{id:t}}),u.NextResponse.json({message:"Pool deleted successfully"},{status:200})}catch(e){return console.error("Error deleting pool:",e),u.NextResponse.json({message:"An error occurred while deleting the pool"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/pools/[poolId]/route",pathname:"/api/quizzes/[id]/pools/[poolId]",filename:"route",bundlePath:"app/api/quizzes/[id]/pools/[poolId]/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/pools/[poolId]/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:h}=x;function g(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},96487:()=>{},78335:()=>{},37702:(e,r,s)=>{"use strict";s.d(r,{N:()=>p});var t=s(46814),i=s(91642),o=s(28053),n=s(7553),u=s(5486),a=s.n(u),d=s(27914);let p={adapter:(0,t.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,o.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,n.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await d.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await a().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let s=await d.db.user.findFirst({where:{email:e.email||""}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,263,452,914],()=>s(89970));module.exports=t})();