(()=>{var e={};e.id=612,e.ids=[612],e.modules={5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},29021:e=>{"use strict";e.exports=require("fs")},33873:e=>{"use strict";e.exports=require("path")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},31421:e=>{"use strict";e.exports=require("node:child_process")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},1708:e=>{"use strict";e.exports=require("node:process")},7066:e=>{"use strict";e.exports=require("node:tty")},57975:e=>{"use strict";e.exports=require("node:util")},41281:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>x,serverHooks:()=>q,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var t={};s.r(t),s.d(t,{POST:()=>d});var i=s(42706),o=s(28203),u=s(45994),a=s(39187),n=s(5486),p=s.n(n),c=s(27914);async function d(e){try{let{name:r,email:s,password:t}=await e.json();if(!r||!s||!t)return a.NextResponse.json({message:"Missing required fields"},{status:400});if(await c.db.user.findUnique({where:{email:s}}))return a.NextResponse.json({message:"User with this email already exists"},{status:409});let i=await p().hash(t,10),{password:o,...u}=await c.db.user.create({data:{name:r,email:s,password:i}});return a.NextResponse.json({message:"User registered successfully",user:u},{status:201})}catch(e){return console.error("Registration error:",e),a.NextResponse.json({message:"An error occurred during registration"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/auth/register/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:q}=x;function h(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,452,914],()=>s(41281));module.exports=t})();