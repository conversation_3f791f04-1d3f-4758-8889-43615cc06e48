"use client";

import { useState } from "react";
import { Question } from "@/generated/prisma";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { generateUUID } from "@/lib/utils";
import QuestionForm from "./QuestionForm";

interface QuestionsManagerProps {
  questions: Question[];
  onAddQuestion: (newQuestion: Omit<Question, "id" | "quizId" | "createdAt" | "updatedAt">) => void;
  onUpdateQuestion: (questionId: string, updatedQuestion: Partial<Question>) => void;
  onDeleteQuestion: (questionId: string) => void;
  isSaving: boolean;
}

export default function QuestionsManager({
  questions,
  onAddQuestion,
  onUpdateQuestion,
  onDeleteQuestion,
  isSaving,
}: QuestionsManagerProps) {
  const [activeTab, setActiveTab] = useState("existing");
  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(null);
  const [questionType, setQuestionType] = useState<string>("multiple_choice");

  const handleAddQuestion = (questionData: any) => {
    const newQuestion = {
      questionId: generateUUID(),
      type: questionType,
      ...questionData,
    };

    onAddQuestion(newQuestion);
    setActiveTab("existing");
  };

  const handleUpdateQuestion = (questionData: any) => {
    if (selectedQuestionId) {
      onUpdateQuestion(selectedQuestionId, questionData);
      setSelectedQuestionId(null);
    }
  };

  const handleDeleteQuestion = (questionId: string) => {
    if (confirm("Are you sure you want to delete this question?")) {
      onDeleteQuestion(questionId);
      if (selectedQuestionId === questionId) {
        setSelectedQuestionId(null);
      }
    }
  };

  const selectedQuestion = selectedQuestionId
    ? questions.find((q) => q.id === selectedQuestionId)
    : null;

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 w-full max-w-md">
          <TabsTrigger value="existing">Existing Questions</TabsTrigger>
          <TabsTrigger value="add">Add Question</TabsTrigger>
        </TabsList>

        <TabsContent value="existing">
          <div className="space-y-6">
            {questions.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  No questions yet. Add your first question to get started.
                </p>
                <Button onClick={() => setActiveTab("add")}>
                  Add First Question
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-md p-4 h-[500px] overflow-y-auto">
                  <h3 className="font-medium mb-4">Questions ({questions.length})</h3>
                  <ul className="space-y-2">
                    {questions.map((question) => (
                      <li
                        key={question.id}
                        className={`p-3 border rounded-md cursor-pointer transition-colors ${
                          selectedQuestionId === question.id
                            ? "border-primary bg-primary/5"
                            : "hover:border-primary/50"
                        }`}
                        onClick={() => setSelectedQuestionId(question.id)}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium line-clamp-1">
                              {typeof question.text === "string"
                                ? question.text
                                : (question.text as any)?.default || "Question"}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {question.type} • {question.points} points
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteQuestion(question.id);
                            }}
                          >
                            <span className="sr-only">Delete</span>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </Button>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="border rounded-md p-4 h-[500px] overflow-y-auto">
                  {selectedQuestion ? (
                    <div>
                      <h3 className="font-medium mb-4">Edit Question</h3>
                      <QuestionForm
                        questionType={selectedQuestion.type}
                        initialData={selectedQuestion}
                        onSubmit={handleUpdateQuestion}
                        isSaving={isSaving}
                        submitLabel="Update Question"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground">
                        Select a question to edit
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="add">
          <div className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="questionType" className="text-sm font-medium">
                Question Type
              </label>
              <select
                id="questionType"
                value={questionType}
                onChange={(e) => setQuestionType(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="multiple_choice">Multiple Choice</option>
                <option value="true_false">True/False</option>
                <option value="short_answer">Short Answer</option>
                <option value="matching">Matching</option>
                <option value="fill_in_the_blank">Fill in the Blank</option>
                <option value="essay">Essay</option>
              </select>
            </div>

            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-4">Add New Question</h3>
              <QuestionForm
                questionType={questionType}
                onSubmit={handleAddQuestion}
                isSaving={isSaving}
                submitLabel="Add Question"
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
