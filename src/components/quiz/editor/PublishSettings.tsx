"use client";

import { useState } from "react";
import { Quiz, Question } from "@/generated/prisma";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface PublishSettingsProps {
  quiz: Quiz & { questions: Question[] };
  onPublishStatusChange: (isPublished: boolean) => void;
  isSaving: boolean;
}

export default function PublishSettings({
  quiz,
  onPublishStatusChange,
  isSaving,
}: PublishSettingsProps) {
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handlePublishClick = () => {
    if (quiz.isPublished) {
      // If already published, unpublish immediately
      onPublishStatusChange(false);
    } else {
      // If not published, show confirmation first
      setShowConfirmation(true);
    }
  };

  const handleConfirmPublish = () => {
    onPublishStatusChange(true);
    setShowConfirmation(false);
  };

  const handleCancelPublish = () => {
    setShowConfirmation(false);
  };

  return (
    <div className="space-y-6">
      <div className="p-4 border rounded-md bg-muted/30">
        <h3 className="font-medium mb-2">Current Status</h3>
        <div className="flex items-center space-x-2">
          <div
            className={`w-3 h-3 rounded-full ${
              quiz.isPublished ? "bg-green-500" : "bg-amber-500"
            }`}
          />
          <p>
            {quiz.isPublished
              ? "Published - This quiz is visible to others"
              : "Draft - Only you can see this quiz"}
          </p>
        </div>
      </div>

      {showConfirmation ? (
        <div className="p-4 border rounded-md bg-primary/5">
          <h3 className="font-medium mb-2">Publish Confirmation</h3>
          <p className="mb-4">
            Are you sure you want to publish this quiz? Once published, it will be visible to others.
          </p>
          <div className="flex space-x-4">
            <Button onClick={handleConfirmPublish} disabled={isSaving}>
              {isSaving ? "Publishing..." : "Yes, Publish Quiz"}
            </Button>
            <Button variant="outline" onClick={handleCancelPublish} disabled={isSaving}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex space-x-4">
          <Button
            onClick={handlePublishClick}
            variant={quiz.isPublished ? "outline" : "default"}
            disabled={isSaving}
          >
            {isSaving
              ? quiz.isPublished
                ? "Unpublishing..."
                : "Publishing..."
              : quiz.isPublished
              ? "Unpublish Quiz"
              : "Publish Quiz"}
          </Button>

          {quiz.isPublished && (
            <Button asChild>
              <Link href={`/quiz/${quiz.id}`} target="_blank">
                View Published Quiz
              </Link>
            </Button>
          )}
        </div>
      )}

      <div className="space-y-4">
        <h3 className="font-medium">Publishing Checklist</h3>
        <ul className="space-y-2">
          <li className="flex items-start space-x-2">
            <div className={`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${quiz.title ? "bg-green-100 text-green-600" : "bg-amber-100 text-amber-600"}`}>
              {quiz.title ? "✓" : "!"}
            </div>
            <div>
              <p className="font-medium">Quiz Title</p>
              <p className="text-sm text-muted-foreground">
                {quiz.title ? "Title is set" : "Quiz needs a title"}
              </p>
            </div>
          </li>

          <li className="flex items-start space-x-2">
            <div className={`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${quiz.questions.length > 0 ? "bg-green-100 text-green-600" : "bg-amber-100 text-amber-600"}`}>
              {quiz.questions.length > 0 ? "✓" : "!"}
            </div>
            <div>
              <p className="font-medium">Questions</p>
              <p className="text-sm text-muted-foreground">
                {quiz.questions.length > 0
                  ? `Quiz has ${quiz.questions.length} question(s)`
                  : "Quiz needs at least one question"}
              </p>
            </div>
          </li>

          <li className="flex items-start space-x-2">
            <div className={`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${quiz.passingScore !== null ? "bg-green-100 text-green-600" : "bg-amber-100 text-amber-600"}`}>
              {quiz.passingScore !== null ? "✓" : "!"}
            </div>
            <div>
              <p className="font-medium">Passing Score</p>
              <p className="text-sm text-muted-foreground">
                {quiz.passingScore !== null
                  ? `Passing score is set to ${quiz.passingScore}%`
                  : "Consider setting a passing score"}
              </p>
            </div>
          </li>

          <li className="flex items-start space-x-2">
            <div className={`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${quiz.timeLimit !== null ? "bg-green-100 text-green-600" : "bg-amber-100 text-amber-600"}`}>
              {quiz.timeLimit !== null ? "✓" : "!"}
            </div>
            <div>
              <p className="font-medium">Time Limit</p>
              <p className="text-sm text-muted-foreground">
                {quiz.timeLimit !== null
                  ? `Time limit is set to ${quiz.timeLimit} minutes`
                  : "Consider setting a time limit"}
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>
  );
}
