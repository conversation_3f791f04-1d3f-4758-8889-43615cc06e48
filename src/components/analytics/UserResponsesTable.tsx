"use client";

import { UserResponse } from "@/generated/prisma";
import { formatDate } from "@/lib/utils";

interface UserResponseWithUser extends UserResponse {
  user: {
    id: string;
    name: string | null;
    email: string | null;
  } | null;
}

interface UserResponsesTableProps {
  responses: UserResponseWithUser[];
}

export default function UserResponsesTable({ responses }: UserResponsesTableProps) {
  if (responses.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No responses yet</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left py-3 px-4 font-medium">User</th>
            <th className="text-left py-3 px-4 font-medium">Score</th>
            <th className="text-left py-3 px-4 font-medium">Time Spent</th>
            <th className="text-left py-3 px-4 font-medium">Started</th>
            <th className="text-left py-3 px-4 font-medium">Completed</th>
            <th className="text-left py-3 px-4 font-medium">Status</th>
          </tr>
        </thead>
        <tbody>
          {responses.map((response) => (
            <tr key={response.id} className="border-b hover:bg-muted/50">
              <td className="py-3 px-4">
                {response.user?.name || response.user?.email || "Anonymous"}
              </td>
              <td className="py-3 px-4">{response.score.toFixed(1)}%</td>
              <td className="py-3 px-4">
                {response.timeSpent
                  ? formatTimeSpent(response.timeSpent)
                  : "N/A"}
              </td>
              <td className="py-3 px-4">{formatDate(response.startedAt)}</td>
              <td className="py-3 px-4">
                {response.completedAt ? formatDate(response.completedAt) : "N/A"}
              </td>
              <td className="py-3 px-4">
                <span
                  className={`inline-block px-2 py-1 text-xs rounded-full ${
                    response.completedAt
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
                      : "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100"
                  }`}
                >
                  {response.completedAt ? "Completed" : "In Progress"}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

function formatTimeSpent(seconds: number): string {
  if (seconds < 60) {
    return `${seconds} sec`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes < 60) {
    return `${minutes} min ${remainingSeconds} sec`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  return `${hours} hr ${remainingMinutes} min`;
}
